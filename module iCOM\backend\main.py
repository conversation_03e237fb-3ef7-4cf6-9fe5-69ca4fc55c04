"""
Serveur FastAPI pour contrôler l'ICOM IC-R8600
Interface REST pour communication CI-V et enregistrement audio
"""

import os
import logging
import asyncio
import json
import time
import base64
import numpy as np
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles

from models import (
    CommandRequest, CommandResponse, RadioStatusResponse,
    ScanRequest, AudioRecordingRequest, AudioRecordingResponse,
    AudioRecordingStatus, RecordingInfo, AudioDevice, ErrorResponse
)
from icom_handler import ICOMHandler
from network_handler import ICOMNetworkBridge, NetworkConfig
from audio_recorder import AudioRecorder

# Import du contrôleur volume haut-parleurs Windows
try:
    from windows_speaker_control import speaker_controller
    WINDOWS_SPEAKER_AVAILABLE = True
    print("SUCCESS: Contrôle volume haut-parleurs Windows disponible")
except ImportError as e:
    WINDOWS_SPEAKER_AVAILABLE = False
    print(f"WARNING: Contrôle volume haut-parleurs Windows non disponible: {e}")

# Configuration
app = FastAPI(
    title="ICOM IC-R8600 Controller API",
    description="API REST pour contrôler le récepteur ICOM IC-R8600 via CI-V",
    version="1.0.0"
)

# Configuration CORS pour le frontend React et la plateforme C2 EW
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Plateforme C2 EW
        "http://localhost:5173",  # Module iCOM standalone
        "http://localhost:8080"   # Serveur Python
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Instances globales
icom_handler = None
network_bridge = None
audio_recorder = None
use_network_mode = False  # Flag pour choisir le mode de communication

# Gestionnaire de connexions WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.audio_streaming = False

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Connexion fermée, la supprimer
                self.disconnect(connection)

    async def broadcast_audio_data(self, audio_data: bytes):
        if self.active_connections:
            # Encoder les données audio en base64 pour transmission WebSocket
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            message = json.dumps({
                "type": "audio_data",
                "data": audio_b64,
                "timestamp": time.time()
            })
            await self.broadcast(message)

manager = ConnectionManager()

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend.log')
    ]
)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    global icom_handler, network_bridge, audio_recorder, use_network_mode
    
    # Initialiser le handler ICOM
    # Configuration pour votre IC-R8600 via USB série
    icom_handler = ICOMHandler(
        port="COM6",  # Port série USB - VÉRIFIEZ dans Gestionnaire de périphériques
        baudrate=19200,
        use_udp=False,  # Connexion série USB
        udp_host="***********",  # Adresse IP (non utilisée en série)
        udp_port=50001  # Port CI-V (non utilisé en série)
    )
    
    # Tenter la connexion
    if icom_handler.connect():
        logger.info("Connexion ICOM établie")
    else:
        logger.warning("Impossible de se connecter à l'ICOM")
    
    # Initialiser l'enregistreur audio
    audio_recorder = AudioRecorder()
    logger.info("Enregistreur audio initialisé")

@app.on_event("shutdown")
async def shutdown_event():
    """Nettoyage à l'arrêt"""
    global icom_handler, audio_recorder
    
    if icom_handler:
        icom_handler.disconnect()
    
    if audio_recorder and audio_recorder.is_recording:
        audio_recorder.stop_recording()

# Routes API

@app.get("/", response_model=dict)
async def root():
    """Page d'accueil de l'API"""
    return {
        "message": "ICOM IC-R8600 Controller API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Vérification de l'état de santé du service"""
    try:
        # Vérifier la connexion radio
        radio_connected = icom_handler.is_connected() if icom_handler else False

        # Vérifier l'enregistreur audio
        audio_available = audio_recorder is not None

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "radio": "connected" if radio_connected else "disconnected",
                "audio": "available" if audio_available else "unavailable",
                "api": "running"
            },
            "version": "1.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "version": "1.0.0"
        }

@app.get("/radio/status")
async def get_radio_status():
    """Alias pour /api/status - compatibilité avec la plateforme C2 EW"""
    return await get_status()

@app.post("/api/command", response_model=CommandResponse)
async def send_command(command: CommandRequest):
    """Envoie une commande au récepteur ICOM"""
    logger.info(f"CONFIG: Commande recue: {command}")

    if not icom_handler:
        logger.error("ERROR: Handler ICOM non initialisé")
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")
    
    try:
        results = []
        
        # Traitement des commandes
        if command.power_on is not None:
            if command.power_on:
                success = icom_handler.power_on()
                results.append(f"Power ON: {'OK' if success else 'ERREUR'}")
            else:
                success = icom_handler.power_off()
                results.append(f"Power OFF: {'OK' if success else 'ERREUR'}")
        
        if command.frequency is not None:
            success = icom_handler.set_frequency(command.frequency)
            results.append(f"Fréquence {command.frequency} Hz: {'OK' if success else 'ERREUR'}")
        
        if command.mode is not None:
            success = icom_handler.set_mode(command.mode)
            results.append(f"Mode {command.mode}: {'OK' if success else 'ERREUR'}")
        
        if command.rf_gain is not None:
            success = icom_handler.set_rf_gain(command.rf_gain)
            results.append(f"RF Gain {command.rf_gain}: {'OK' if success else 'ERREUR'}")

        if command.af_gain is not None:
            success = icom_handler.set_af_gain(command.af_gain)
            results.append(f"AF Gain {command.af_gain}: {'OK' if success else 'ERREUR'}")

        # Gestion des commandes de scan
        if command.scan_start is not None and command.scan_start:
            logger.info(f"SCAN: Démarrage scan programmé")

            # Si on a les paramètres de scan, les utiliser
            if (command.scan_start_frequency is not None and
                command.scan_end_frequency is not None):

                start_freq = command.scan_start_frequency
                end_freq = command.scan_end_frequency
                step_freq = command.scan_step or 25000
                scan_mode = command.scan_mode or 'FM'

                logger.info(f"DATA: Scan: {start_freq} Hz → {end_freq} Hz (pas: {step_freq} Hz, mode: {scan_mode})")

                try:
                    # Utiliser la méthode de scan de l'ICOM handler
                    success = icom_handler.start_scan(start_freq, end_freq, step_freq, scan_mode)
                    results.append(f"Scan programmé: {'OK' if success else 'ERREUR'}")

                except Exception as e:
                    logger.error(f"ERROR: Erreur scan: {e}")
                    results.append(f"Scan Error: {str(e)}")
            else:
                # Scan simple sans paramètres
                try:
                    success = icom_handler.start_simple_scan()
                    results.append(f"Scan simple: {'OK' if success else 'ERREUR'}")
                except Exception as e:
                    logger.error(f"ERROR: Erreur scan simple: {e}")
                    results.append(f"Scan Simple Error: {str(e)}")

        # Gestion des commandes CI-V spécifiques
        if command.ci_v_command is not None:
            if command.ci_v_command == '06' and command.mode is not None and command.filter is not None:
                # Commande CI-V 06: Set Mode + Filter
                # Format: FE FE 94 E0 06 <mode> <filter> FD

                logger.info(f"CONFIG: Commande CI-V 06: Mode {command.mode} + Filter {command.filter}")

                try:
                    # Construire la commande CI-V complète avec adresse de votre récepteur
                    ci_v_data = [
                        0xFE, 0xFE,           # Preamble
                        0x94,                 # Adresse récepteur IC-R8600 (votre configuration)
                        0xE0,                 # Adresse contrôleur standard
                        0x06,                 # Commande: Set Mode + Filter
                        int(command.mode, 16), # Mode (hex string vers int)
                        int(command.filter, 16), # Filter (hex string vers int)
                        0xFD                  # Postamble
                    ]

                    # Envoyer la commande CI-V brute
                    success = icom_handler.send_raw_command(ci_v_data)

                    filter_names = {'01': 'FIL1', '02': 'FIL2', '03': 'FIL3'}
                    filter_name = filter_names.get(command.filter, f'Filter {command.filter}')

                    results.append(f"CI-V Filter {filter_name}: {'OK' if success else 'ERREUR'}")

                    if command.description:
                        logger.info(f"WRITE: {command.description}")

                except ValueError as e:
                    logger.error(f"ERROR: Erreur format commande CI-V: {e}")
                    results.append(f"CI-V Format Error: {str(e)}")
                except Exception as e:
                    logger.error(f"ERROR: Erreur commande CI-V: {e}")
                    results.append(f"CI-V Error: {str(e)}")

            elif command.ci_v_command == '05':
                # Commande CI-V 05: Set frequency
                logger.info(f"CONFIG: Commande CI-V 05: Set frequency")

                try:
                    ci_v_data = [0xFE, 0xFE, 0x94, 0xE0, 0x05]

                    # Ajouter fréquence BCD si fournie
                    if hasattr(command, 'frequency_bcd') and command.frequency_bcd:
                        logger.info(f"DATA: Fréquence BCD reçue: {command.frequency_bcd}")
                        for bcd_byte in command.frequency_bcd:
                            ci_v_data.append(int(bcd_byte, 16))

                    ci_v_data.append(0xFD)

                    logger.info(f"SEND: Envoi commande CI-V 05: {[hex(b) for b in ci_v_data]}")
                    success = icom_handler.send_raw_command(ci_v_data)
                    results.append(f"CI-V Set Frequency: {'OK' if success else 'ERREUR'}")

                except Exception as e:
                    logger.error(f"ERROR: Erreur commande CI-V 05: {e}")
                    results.append(f"CI-V Frequency Error: {str(e)}")

            elif command.ci_v_command == '1A':
                # Commandes CI-V 1A: Memory/Scan operations
                logger.info(f"SCAN: Commande CI-V 1A: {command.description or 'Scan operation'}")

                try:
                    ci_v_data = [0xFE, 0xFE, 0x94, 0xE0, 0x1A]

                    # Ajouter sub-commande
                    if hasattr(command, 'sub_command') and command.sub_command:
                        sub_cmd = int(command.sub_command, 16)
                        ci_v_data.append(sub_cmd)
                        logger.info(f"DATA: Sub-commande: {command.sub_command} → {hex(sub_cmd)}")

                    # Ajouter données
                    if hasattr(command, 'data') and command.data:
                        data_byte = int(command.data, 16)
                        ci_v_data.append(data_byte)
                        logger.info(f"DATA: Données: {command.data} → {hex(data_byte)}")

                    # Ajouter postamble
                    ci_v_data.append(0xFD)

                    logger.info(f"SEND: Envoi commande CI-V 1A: {[hex(b) for b in ci_v_data]}")

                    # Envoyer la commande
                    success = icom_handler.send_raw_command(ci_v_data)
                    results.append(f"CI-V Scan: {'OK' if success else 'ERREUR'}")

                    if command.description:
                        logger.info(f"WRITE: {command.description}")

                except Exception as e:
                    logger.error(f"ERROR: Erreur commande CI-V 1A: {e}")
                    results.append(f"CI-V Scan Error: {str(e)}")

            elif command.ci_v_command == '10':
                # Commande CI-V 10: Set step frequency
                logger.info(f"CONFIG: Commande CI-V 10: Set step frequency")

                try:
                    ci_v_data = [
                        0xFE, 0xFE, 0x94, 0xE0, 0x10,
                        int(getattr(command, 'step_code', '07'), 16),  # Code pas par défaut
                        0xFD
                    ]

                    success = icom_handler.send_raw_command(ci_v_data)
                    results.append(f"CI-V Step: {'OK' if success else 'ERREUR'}")

                except Exception as e:
                    logger.error(f"ERROR: Erreur commande CI-V 10: {e}")
                    results.append(f"CI-V Step Error: {str(e)}")

            else:
                results.append(f"CI-V Command {command.ci_v_command}: Format non supporté")

        return CommandResponse(
            success=True,
            message="; ".join(results) if results else "Aucune commande spécifiée",
            data={"timestamp": datetime.now().isoformat()}
        )
        
    except Exception as e:
        logger.error(f"Erreur commande: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/scan/start", response_model=CommandResponse)
async def start_scan(scan_request: ScanRequest):
    """Démarre un scan de fréquences"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        # Configurer le mode si spécifié
        if scan_request.mode:
            icom_handler.set_mode(scan_request.mode.value)

        success = icom_handler.start_scan(
            scan_request.start_frequency,
            scan_request.end_frequency,
            scan_request.step
        )

        return CommandResponse(
            success=success,
            message=f"Scan {'démarré' if success else 'échec'} de {scan_request.start_frequency} à {scan_request.end_frequency} Hz",
            data={
                "start_freq": scan_request.start_frequency,
                "end_freq": scan_request.end_frequency,
                "step": scan_request.step,
                "mode": scan_request.mode.value if scan_request.mode else None
            }
        )
    except Exception as e:
        logger.error(f"Erreur scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))



@app.post("/api/scan/set-start-frequency", response_model=CommandResponse)
async def set_scan_start_frequency(request: dict):
    """Programme la fréquence de début de scan individuellement"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        frequency = request.get("frequency")
        mode = request.get("mode", "FM")

        if not frequency:
            raise HTTPException(status_code=400, detail="Fréquence requise")

        success = icom_handler.set_scan_start_frequency(int(frequency), mode)
        return CommandResponse(
            success=success,
            message=f"Fréquence début {'programmée' if success else 'échec'}: {frequency} Hz"
        )
    except Exception as e:
        logger.error(f"Erreur programmation fréquence début: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/scan/set-end-frequency", response_model=CommandResponse)
async def set_scan_end_frequency(request: dict):
    """Programme la fréquence de fin de scan individuellement"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        frequency = request.get("frequency")
        mode = request.get("mode", "FM")

        if not frequency:
            raise HTTPException(status_code=400, detail="Fréquence requise")

        success = icom_handler.set_scan_end_frequency(int(frequency), mode)
        return CommandResponse(
            success=success,
            message=f"Fréquence fin {'programmée' if success else 'échec'}: {frequency} Hz"
        )
    except Exception as e:
        logger.error(f"Erreur programmation fréquence fin: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/scan/set-step", response_model=CommandResponse)
async def set_scan_step(request: dict):
    """Configure le pas de scan individuellement"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        step = request.get("step")

        if not step:
            raise HTTPException(status_code=400, detail="Pas requis")

        success = icom_handler.set_scan_step(int(step))
        return CommandResponse(
            success=success,
            message=f"Pas {'configuré' if success else 'échec'}: {step} Hz"
        )
    except Exception as e:
        logger.error(f"Erreur configuration pas: {e}")
        raise HTTPException(status_code=500, detail=str(e))



@app.post("/api/scan/progressive", response_model=CommandResponse)
async def start_progressive_scan(request: ScanRequest):
    """Lance un scan progressif fréquence par fréquence"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        success = icom_handler.start_progressive_scan(
            start_freq=request.start_frequency,
            end_freq=request.end_frequency,
            step=request.step,
            mode=request.mode
        )

        return CommandResponse(
            success=success,
            message=f"Scan progressif {'démarré' if success else 'échec'}: {request.start_frequency}-{request.end_frequency} Hz"
        )

    except Exception as e:
        logger.error(f"Erreur scan progressif: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/scan/stop", response_model=CommandResponse)
async def stop_scan():
    """Arrête le scan en cours (CI-V et progressif)"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        # Arrêter le scan CI-V classique
        success1 = icom_handler.stop_scan()

        # Arrêter le scan progressif
        success2 = icom_handler.stop_progressive_scan()

        success = success1 or success2

        return CommandResponse(
            success=success,
            message=f"Scan {'arrêté' if success else 'échec arrêt'}"
        )

    except Exception as e:
        logger.error(f"Erreur arrêt scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/connection/status")
async def get_connection_status():
    """Vérifie l'état de la connexion ICOM"""
    if not icom_handler:
        return {"connected": False, "error": "Handler non initialisé"}

    return {
        "connected": icom_handler.is_connected,
        "simulation_mode": icom_handler.simulation_mode,
        "port": icom_handler.port,
        "use_udp": icom_handler.use_udp,
        "serial_available": icom_handler.serial_conn is not None,
        "udp_available": icom_handler.udp_socket is not None
    }

@app.post("/api/connection/test")
async def test_connection():
    """Test une commande simple pour vérifier la connexion"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        # Test avec une commande simple (lecture fréquence)
        success = icom_handler.get_frequency()
        return {
            "success": success is not None,
            "frequency": success,
            "connected": icom_handler.is_connected,
            "simulation": icom_handler.simulation_mode or not icom_handler.is_connected
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

# Routes Audio

@app.post("/api/audio/start", response_model=AudioRecordingResponse)
async def start_audio_recording(recording_request: AudioRecordingRequest):
    """Démarre l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.start_recording(
            audio_type=recording_request.audio_type.value,
            device_id=recording_request.device_id
        )

        if result["success"]:
            return AudioRecordingResponse(**result)
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Erreur démarrage enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/audio/stop", response_model=AudioRecordingResponse)
async def stop_audio_recording():
    """Arrête l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.stop_recording()

        if result["success"]:
            return AudioRecordingResponse(**result)
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Erreur arrêt enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio/status", response_model=AudioRecordingStatus)
async def get_audio_status():
    """Récupère l'état de l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        status = audio_recorder.get_recording_status()
        return AudioRecordingStatus(**status)
    except Exception as e:
        logger.error(f"Erreur état enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings", response_model=List[RecordingInfo])
async def list_recordings():
    """Liste tous les enregistrements disponibles"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        recordings = audio_recorder.list_recordings()
        return [RecordingInfo(**rec) for rec in recordings]
    except Exception as e:
        logger.error(f"Erreur listage enregistrements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings/{filename}")
async def download_recording(filename: str):
    """Télécharge un fichier d'enregistrement"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        filepath = os.path.join(audio_recorder.recordings_dir, filename)
        if os.path.exists(filepath):
            return FileResponse(
                filepath,
                media_type="audio/wav",
                filename=filename
            )
        else:
            raise HTTPException(status_code=404, detail="Fichier non trouvé")
    except Exception as e:
        logger.error(f"Erreur téléchargement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/recordings/{filename}", response_model=CommandResponse)
async def delete_recording(filename: str):
    """Supprime un enregistrement"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.delete_recording(filename)
        return CommandResponse(**result)
    except Exception as e:
        logger.error(f"Erreur suppression: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio/devices", response_model=List[AudioDevice])
async def get_audio_devices():
    """Liste les périphériques audio disponibles"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        devices = audio_recorder.get_audio_devices()
        return [AudioDevice(**device) for device in devices]
    except Exception as e:
        logger.error(f"Erreur listage périphériques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status", response_model=RadioStatusResponse)
async def get_status():
    """Récupère l'état actuel du récepteur"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        status = icom_handler.get_status()
        return RadioStatusResponse(
            frequency=status["frequency"],
            mode=status["mode"],
            rssi=status["rssi"],
            power_on=status["power_on"],
            rf_gain=status["rf_gain"],
            filter_width=status["filter_width"],
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Erreur lecture état: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/frequency")
async def get_current_frequency():
    """Récupère uniquement la fréquence actuelle (plus rapide pour le scan)"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    freq = icom_handler.status.frequency
    scan_active = getattr(icom_handler, 'scan_active', False)

    # Log pour déboguer
    if scan_active:
        print(f"API /frequency: {freq} Hz ({freq/1000000:.3f} MHz)")

    return {
        "frequency": freq,
        "scan_active": scan_active
    }

# WebSocket pour streaming audio en temps réel
@app.websocket("/ws/audio")
async def websocket_audio_endpoint(websocket: WebSocket):
    """WebSocket pour streaming audio en temps réel"""
    await manager.connect(websocket)
    try:
        while True:
            # Recevoir les commandes du client
            data = await websocket.receive_text()
            message = json.loads(data)

            if message.get("type") == "start_stream":
                # Démarrer le streaming audio
                if audio_recorder and not manager.audio_streaming:
                    manager.audio_streaming = True
                    # Démarrer la capture audio en arrière-plan
                    asyncio.create_task(stream_audio_data())
                    await websocket.send_text(json.dumps({
                        "type": "stream_started",
                        "message": "Streaming audio démarré"
                    }))

            elif message.get("type") == "stop_stream":
                # Arrêter le streaming audio
                manager.audio_streaming = False
                await websocket.send_text(json.dumps({
                    "type": "stream_stopped",
                    "message": "Streaming audio arrêté"
                }))

    except WebSocketDisconnect:
        manager.disconnect(websocket)
        if len(manager.active_connections) == 0:
            manager.audio_streaming = False

async def stream_audio_data():
    """Fonction pour streamer les données audio du récepteur ICOM via USB"""
    global icom_handler

    try:
        # Méthode 1: Essayer d'activer l'audio streaming USB sur le récepteur ICOM
        if icom_handler:
            logger.info("AUDIO: Tentative d'activation audio streaming USB sur IC-R8600...")
            audio_usb_success = icom_handler.start_audio_stream()

            if audio_usb_success:
                logger.info("AUDIO: Audio streaming USB activé - lecture des données...")

                # Boucle pour lire les données audio depuis le port série USB
                while manager.audio_streaming:
                    audio_data = icom_handler.get_audio_data()

                    if audio_data and len(audio_data) > 0:
                        # Envoyer les données audio via WebSocket
                        await manager.broadcast_audio_data(audio_data)

                    await asyncio.sleep(0.01)  # Petite pause

                # Désactiver l'audio streaming à la fin
                icom_handler.stop_audio_stream()
                return

        # Méthode 2: Fallback vers périphérique audio système si USB audio non disponible
        logger.info("AUDIO: Fallback vers périphérique audio système...")
        import sounddevice as sd

        # Chercher un périphérique ICOM ou USB audio
        devices = sd.query_devices()
        logger.info("AUDIO: Périphériques audio disponibles:")
        for i, device in enumerate(devices):
            logger.info(f"  {i}: {device['name']} - {device['max_input_channels']} entrées")

        # Chercher spécifiquement un périphérique ICOM ou USB
        input_device = None
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:
                device_name = device['name'].lower()
                # Priorité aux périphériques ICOM, IC-R8600, USB Audio CODEC
                if any(keyword in device_name for keyword in ['icom', 'ic-r8600', 'ic-r', 'usb audio codec']):
                    input_device = i
                    logger.info(f"AUDIO: Périphérique ICOM détecté: {device['name']}")
                    break
                elif any(keyword in device_name for keyword in ['usb audio', 'line', 'stereo mix', 'what u hear']):
                    if input_device is None:  # Utiliser comme fallback
                        input_device = i
                        logger.info(f"AUDIO: Périphérique audio USB détecté: {device['name']}")
                        break

        if input_device is None:
            input_device = sd.default.device[0]
            logger.info(f"AUDIO: Utilisation du périphérique par défaut: {devices[input_device]['name']}")

        # Configurer les paramètres audio selon le périphérique
        device_name = devices[input_device]['name'].lower()
        if 'usb audio codec' in device_name:
            # Paramètres optimisés pour IC-R8600
            channels = 1
            samplerate = 48000
            blocksize = 512
            logger.info("AUDIO: Configuration optimisée pour IC-R8600 détectée")
        else:
            # Paramètres par défaut
            channels = 1
            samplerate = 44100
            blocksize = 1024

        def audio_callback(indata, frames, time, status):
            if status:
                logger.warning(f"Audio callback status: {status}")

            if manager.audio_streaming and manager.active_connections:
                # Convertir les données audio en bytes
                audio_bytes = (indata * 32767).astype(np.int16).tobytes()
                # Envoyer via WebSocket
                asyncio.create_task(manager.broadcast_audio_data(audio_bytes))

        # Démarrer le stream audio avec les paramètres optimisés
        with sd.InputStream(
            device=input_device,
            callback=audio_callback,
            channels=channels,
            samplerate=samplerate,
            blocksize=blocksize,
            dtype=np.float32
        ):
            logger.info(f"AUDIO: Streaming audio démarré depuis {devices[input_device]['name']} ({samplerate}Hz, {channels}ch)")
            while manager.audio_streaming:
                await asyncio.sleep(0.01)

    except Exception as e:
        logger.error(f"ERROR: Erreur streaming audio: {e}")
        logger.info("INFO: Vérifiez la connexion USB du récepteur ICOM IC-R8600")
        manager.audio_streaming = False

# WebSocket général pour statut et commandes
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket pour communication générale"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            # Echo du message pour test
            await websocket.send_text(json.dumps({
                "type": "echo",
                "data": message,
                "timestamp": time.time()
            }))

    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Endpoint pour contrôler le volume des haut-parleurs Windows
@app.post("/set_speaker_volume")
async def set_speaker_volume(request: dict):
    """Définit le volume des haut-parleurs Windows"""
    try:
        volume = request.get('volume', 50)

        if not isinstance(volume, int) or volume < 0 or volume > 100:
            raise HTTPException(status_code=400, detail="Volume doit être entre 0 et 100")

        if not WINDOWS_SPEAKER_AVAILABLE:
            return {
                "success": False,
                "message": "Contrôle volume haut-parleurs non disponible sur ce système"
            }

        success = speaker_controller.set_volume(volume)

        if success:
            return {
                "success": True,
                "volume": volume,
                "message": f"Volume haut-parleurs défini à {volume}%"
            }
        else:
            return {
                "success": False,
                "message": "Échec du changement de volume"
            }

    except Exception as e:
        logger.error(f"Erreur set_speaker_volume: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/get_speaker_volume")
async def get_speaker_volume():
    """Récupère le volume actuel des haut-parleurs Windows"""
    try:
        if not WINDOWS_SPEAKER_AVAILABLE:
            return {
                "success": False,
                "message": "Contrôle volume haut-parleurs non disponible sur ce système"
            }

        volume = speaker_controller.get_volume()

        if volume is not None:
            return {
                "success": True,
                "volume": volume,
                "message": f"Volume actuel: {volume}%"
            }
        else:
            return {
                "success": False,
                "message": "Impossible de lire le volume"
            }

    except Exception as e:
        logger.error(f"Erreur get_speaker_volume: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Endpoints pour contrôler le volume Windows (haut-parleurs Realtek)
@app.post("/set_windows_volume")
async def set_windows_volume(request: dict):
    """Contrôle le volume des haut-parleurs Windows Realtek"""
    try:
        volume = request.get('volume', 50)

        if not isinstance(volume, int) or volume < 0 or volume > 100:
            raise HTTPException(status_code=400, detail="Volume doit être entre 0 et 100")

        # Utiliser pycaw pour contrôler le volume Windows directement
        try:
            from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
            from comtypes import CLSCTX_ALL, CoInitialize, CoUninitialize
            from ctypes import cast, POINTER
            import time

            logger.info(f"🎯 Demande changement volume: {volume}%")

            # Initialiser COM avec gestion d'erreur
            try:
                CoInitialize()
                com_initialized = True
            except:
                # COM déjà initialisé, continuer
                com_initialized = False
                logger.info("COM déjà initialisé")

            try:
                # Obtenir le périphérique audio par défaut (haut-parleurs Realtek)
                devices = AudioUtilities.GetSpeakers()
                interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
                volume_interface = cast(interface, POINTER(IAudioEndpointVolume))

                # Lire le volume actuel
                current_volume_before = volume_interface.GetMasterScalarVolume()
                logger.info(f"DATA: Volume actuel avant: {int(current_volume_before * 100)}%")

                # Convertir le pourcentage en niveau de volume (0.0 à 1.0)
                volume_level = volume / 100.0

                # Définir le volume directement (pas de touches virtuelles)
                volume_interface.SetMasterScalarVolume(volume_level, None)
                logger.info(f"🎯 Volume défini à: {volume_level:.3f}")

                # Attendre que le changement soit appliqué
                time.sleep(0.2)

                # Vérifier le volume défini
                current_volume_after = volume_interface.GetMasterScalarVolume()
                actual_percentage = int(current_volume_after * 100)

                logger.info(f"SUCCESS: Volume Windows Realtek: {volume}% → {actual_percentage}%")

                return {
                    "success": True,
                    "volume": actual_percentage,
                    "requested": volume,
                    "before": int(current_volume_before * 100),
                    "after": actual_percentage,
                    "message": f"Volume fixé: {volume}% → {actual_percentage}%"
                }

            finally:
                # Nettoyer COM seulement si on l'a initialisé
                if com_initialized:
                    try:
                        CoUninitialize()
                    except:
                        pass

        except ImportError as e:
            logger.error(f"ERROR: pycaw non disponible: {e}")
            return {
                "success": False,
                "message": f"pycaw non installé: {e}"
            }

        except Exception as e:
            logger.error(f"ERROR: Erreur pycaw: {e}")
            return {
                "success": False,
                "message": f"Erreur contrôle volume: {e}"
            }

    except Exception as e:
        logger.error(f"Erreur set_windows_volume: {e}")
        return {
            "success": False,
            "message": f"Erreur: {str(e)}"
        }

@app.post("/set_windows_mute")
async def set_windows_mute(request: dict):
    """Contrôle le mute des haut-parleurs Windows Realtek"""
    try:
        muted = request.get('muted', False)

        # Utiliser pycaw pour contrôler le mute Windows directement
        try:
            from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
            from comtypes import CLSCTX_ALL, CoInitialize, CoUninitialize
            from ctypes import cast, POINTER

            # Initialiser COM
            CoInitialize()

            try:
                # Obtenir le périphérique audio par défaut (haut-parleurs Realtek)
                devices = AudioUtilities.GetSpeakers()
                interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
                volume_interface = cast(interface, POINTER(IAudioEndpointVolume))

                # Définir le mute directement
                volume_interface.SetMute(1 if muted else 0, None)

                # Attendre un peu pour que le changement soit appliqué
                import time
                time.sleep(0.1)

                # Vérifier l'état mute
                current_mute = volume_interface.GetMute()

                logger.info(f"SUCCESS: Mute Windows Realtek: {'ON' if current_mute else 'OFF'}")

                return {
                    "success": True,
                    "muted": bool(current_mute),
                    "requested": muted,
                    "message": f"Mute haut-parleurs Realtek: {'ON' if current_mute else 'OFF'}"
                }

            finally:
                # Nettoyer COM
                CoUninitialize()

        except ImportError as e:
            logger.error(f"ERROR: pycaw non disponible: {e}")
            return {
                "success": False,
                "message": f"pycaw non installé: {e}"
            }
        except Exception as e:
            logger.error(f"ERROR: Erreur pycaw mute: {e}")
            return {
                "success": False,
                "message": f"Erreur contrôle mute: {e}"
            }

    except Exception as e:
        logger.error(f"Erreur set_windows_mute: {e}")
        return {
            "success": False,
            "message": f"Erreur: {str(e)}"
        }

if __name__ == "__main__":
    import uvicorn
    # Port modifié pour éviter les conflits avec la plateforme principale C2 EW (port 8000)
    uvicorn.run(app, host="0.0.0.0", port=8100, log_level="info")
