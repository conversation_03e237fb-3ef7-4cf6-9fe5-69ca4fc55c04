import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import './c2ew-styles.css';

// Ajout des styles CSS pour l'animation de l'interface mini
const miniInterfaceStyles = `
  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.9);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 5px currentColor;
    }
    50% {
      box-shadow: 0 0 15px currentColor, 0 0 25px currentColor;
    }
  }
`;

// Injecter les styles dans le document
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = miniInterfaceStyles;
  document.head.appendChild(styleSheet);
}

import {
  Power,
  Radio,
  Volume2,
  Settings,
  Play,
  Square,
  Download,
  Trash2,
  RefreshCw,
  Zap,
  Activity,
  Target,
  Scan,
  Mic,
  MicOff,
  Plus,
  Minus,
  RotateCw,
  Headphones,
  Volume1,
  VolumeX,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  HardDrive,
  Cpu,
  MemoryStick,
  Signal,
  Bluetooth,
  BluetoothConnected,
  BluetoothOff,
  Gauge,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  LineChart,
  Monitor,
  Speaker,
  Pause,
  SkipForward,
  SkipBack,
  FastForward,
  Rewind,
  Repeat,
  Shuffle,
  List,
  Grid,
  Filter,
  Search,
  SortAsc,
  SortDesc,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Key,
  Shield,
  ShieldCheck,
  ShieldAlert,
  User,
  Users,
  UserCheck,
  UserX,
  Mail,
  Phone,
  MessageSquare,
  Bell,
  BellOff,
  Calendar,
  Clock3,
  Timer,
  Stopwatch,
  MapPin,
  Navigation,
  Compass,
  Map,
  Globe,
  Satellite,
  Radar,
  Crosshair,
  Focus,
  Maximize,
  Minimize,
  Expand,
  Shrink,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  FlipHorizontal,
  FlipVertical,
  Move,
  MousePointer,
  Hand,
  Grab,
  Copy,
  Cut,
  Paste,
  Clipboard,
  Save,
  Upload,
  Download as DownloadIcon,
  Share,
  Link,
  ExternalLink,
  Bookmark,
  Star,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Tag,
  Hash,
  AtSign,
  Percent,
  DollarSign,
  Euro,
  PoundSterling,
  Yen,
  CreditCard,
  Wallet,
  ShoppingCart,
  ShoppingBag,
  Package,
  Truck,
  Plane,
  Car,
  Bike,
  Train,
  Bus,
  Ship,
  Anchor,
  Home,
  Building,
  Factory,
  Store,
  Hospital,
  School,
  University,
  Church,
  TreePine,
  Flower,
  Sun,
  Moon,
  Cloud,
  CloudRain,
  CloudSnow,
  CloudLightning,
  Umbrella,
  Thermometer,
  Wind,
  Droplets,
  Flame,
  Snowflake,
  Zap as Lightning,
  Battery,
  BatteryLow,
  Plug,
  Cable,
  Usb,
  HardDrive as Storage,
  SdCard,
  Smartphone,
  Tablet,
  Laptop,
  Desktop,
  Tv,
  Camera,
  Video,
  Image,
  Film,
  Music,
  Disc,
  Cassette,
  Radio as RadioIcon,
  Podcast,
  Rss,
  Wifi as WifiIcon,
  Bluetooth as BluetoothIcon,
  Nfc,
  QrCode,
  Barcode,
  Fingerprint,
  Scan as ScanIcon,
  PrinterCheck,
  FileText,
  File,
  Folder,
  FolderOpen,
  Archive,
  Trash,
  Delete,
  Edit,
  Edit2,
  Edit3,
  PenTool,
  Highlighter,
  Type,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List as ListIcon,
  CheckSquare,
  Square as SquareIcon,
  Circle,
  Triangle,
  Hexagon,
  Octagon,
  Diamond,
  Pentagon,
  Star as StarIcon,
  Heart as HeartIcon,
  Smile,
  Frown,
  Meh,
  Angry,
  Laugh,
  Cry,
  Kiss,
  Wink,
  Surprised,
  Confused,
  Sleepy,
  Dizzy,
  Sick,
  Mask,
  Glasses,
  Sunglasses,
  Crown,
  Hat,
  Shirt,
  Tie,
  Dress,
  Shoe,
  Watch,
  Ring,
  Gem,
  Gift,
  Cake,
  Coffee,
  Tea,
  Beer,
  Wine,
  Cocktail,
  Pizza,
  Burger,
  Sandwich,
  Salad,
  Apple,
  Banana,
  Cherry,
  Grape,
  Orange,
  Strawberry,
  Carrot,
  Corn,
  Pepper,
  Tomato,
  Bread,
  Cheese,
  Egg,
  Fish,
  Meat,
  Chicken,
  Milk,
  Honey,
  Salt,
  Sugar,
  Spoon,
  Fork,
  Knife,
  Plate,
  Bowl,
  Cup,
  Glass,
  Bottle,
  Can,
  Jar
} from 'lucide-react';

const C2EWRadioControl = () => {
  // États principaux
  const [isOn, setIsOn] = useState(false);
  const [isMini, setIsMini] = useState(false);
  const [frequency, setFrequency] = useState(7500.120);
  const [mode, setMode] = useState('FM');
  const [volume, setVolume] = useState(50);
  const [squelch, setSquelch] = useState(0);
  const [afGain, setAfGain] = useState(50);
  const [rfGain, setRfGain] = useState(50);
  const [audioLevel, setAudioLevel] = useState(5);
  const [isTransmitting, setIsTransmitting] = useState(false);
  const [isReceiving, setIsReceiving] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isLive, setIsLive] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [signalStrength, setSignalStrength] = useState(0);
  const [audioData, setAudioData] = useState(null);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);

  // États pour les fréquences tactiques
  const [tacticalFrequencies, setTacticalFrequencies] = useState([
    { id: 1, name: 'Canal d\'urgence principal', freq: 145.500, active: true },
    { id: 2, name: 'Coordination trafic', freq: 145.750, active: false },
    { id: 3, name: 'Canal de coordination', freq: 146.000, active: false },
    { id: 4, name: 'Canal de coordination générale', freq: 146.000, active: false },
    { id: 5, name: 'UHF Principal', freq: 433.500, active: false },
    { id: 6, name: 'Canal UHF principal', freq: 433.500, active: false },
    { id: 7, name: 'ISM Band', freq: 868.000, active: false },
    { id: 8, name: 'Canal ISM 868 MHz', freq: 868.000, active: false }
  ]);

  // États pour le scan programmé
  const [scanSettings, setScanSettings] = useState({
    startFreq: 118000000,
    endFreq: 136500000,
    step: 25000,
    isScanning: false,
    currentFreq: 118000000
  });

  // États pour les filtres
  const [filters, setFilters] = useState({
    fil1: false,
    fil2: false,
    fil3: false
  });

  // États pour l'alimentation
  const [powerStatus, setPowerStatus] = useState({
    voltage: 12.5,
    current: 2.1,
    temperature: 45
  });

  // Références pour l'audio
  const audioContextRef = useRef(null);
  const audioSourceRef = useRef(null);
  const audioElementRef = useRef(null);
  const analyserRef = useRef(null);
  const dataArrayRef = useRef(null);
  const animationFrameRef = useRef(null);

  // Configuration de l'API
  const API_BASE_URL = 'http://localhost:8001';

  // Fonction pour initialiser l'audio
  const initializeAudio = useCallback(async () => {
    try {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      // Créer l'analyseur pour la visualisation
      if (!analyserRef.current) {
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 256;
        const bufferLength = analyserRef.current.frequencyBinCount;
        dataArrayRef.current = new Uint8Array(bufferLength);
      }

      console.log('Audio initialisé avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'initialisation audio:', error);
    }
  }, []);

  // Fonction pour démarrer le streaming audio
  const startAudioStream = useCallback(async () => {
    try {
      await initializeAudio();

      if (!audioElementRef.current) {
        audioElementRef.current = new Audio();
        audioElementRef.current.crossOrigin = 'anonymous';
      }

      const audioElement = audioElementRef.current;
      audioElement.src = `${API_BASE_URL}/audio/stream`;
      audioElement.load();

      audioElement.onloadstart = () => console.log('Début du chargement audio');
      audioElement.oncanplay = () => {
        console.log('Audio prêt à être joué');
        audioElement.play().catch(console.error);
      };
      audioElement.onplay = () => {
        console.log('Lecture audio démarrée');
        setIsAudioPlaying(true);

        // Connecter à l'analyseur pour la visualisation
        if (!audioSourceRef.current && audioContextRef.current) {
          audioSourceRef.current = audioContextRef.current.createMediaElementSource(audioElement);
          audioSourceRef.current.connect(analyserRef.current);
          analyserRef.current.connect(audioContextRef.current.destination);
        }

        // Démarrer l'animation de visualisation
        startVisualization();
      };
      audioElement.onpause = () => {
        console.log('Lecture audio en pause');
        setIsAudioPlaying(false);
      };
      audioElement.onerror = (e) => {
        console.error('Erreur audio:', e);
        setIsAudioPlaying(false);
      };

    } catch (error) {
      console.error('Erreur lors du démarrage du streaming audio:', error);
    }
  }, [initializeAudio]);

  // Fonction pour arrêter le streaming audio
  const stopAudioStream = useCallback(() => {
    if (audioElementRef.current) {
      audioElementRef.current.pause();
      audioElementRef.current.src = '';
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    setIsAudioPlaying(false);
    setAudioData(null);
  }, []);

  // Fonction pour la visualisation audio
  const startVisualization = useCallback(() => {
    if (!analyserRef.current || !dataArrayRef.current) return;

    const updateVisualization = () => {
      analyserRef.current.getByteFrequencyData(dataArrayRef.current);

      // Calculer le niveau audio moyen
      const average = dataArrayRef.current.reduce((sum, value) => sum + value, 0) / dataArrayRef.current.length;
      setAudioLevel(Math.round(average / 25.5)); // Normaliser sur 10

      // Mettre à jour les données pour la visualisation
      setAudioData([...dataArrayRef.current]);

      animationFrameRef.current = requestAnimationFrame(updateVisualization);
    };

    updateVisualization();
  }, []);

  // Effet pour la connexion initiale
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/status`);
        if (response.status === 200) {
          setConnectionStatus('connected');
          console.log('Connexion établie avec le backend iCOM');
        }
      } catch (error) {
        setConnectionStatus('disconnected');
        console.log('Backend iCOM non disponible');
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 5000);

    return () => clearInterval(interval);
  }, []);

  // Effet pour la simulation des métriques
  useEffect(() => {
    if (!isOn) return;

    const interval = setInterval(() => {
      setSignalStrength(Math.floor(Math.random() * 100));
      setIsReceiving(Math.random() > 0.8);

      // Simulation des données de puissance
      setPowerStatus(prev => ({
        voltage: 12.3 + Math.random() * 0.4,
        current: 1.8 + Math.random() * 0.6,
        temperature: 40 + Math.random() * 10
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [isOn]);

  // Fonction pour changer la fréquence
  const handleFrequencyChange = async (newFreq) => {
    setFrequency(newFreq);

    if (connectionStatus === 'connected') {
      try {
        await axios.post(`${API_BASE_URL}/frequency`, { frequency: newFreq * 1000000 });
        console.log(`Fréquence changée: ${newFreq} MHz`);
      } catch (error) {
        console.error('Erreur lors du changement de fréquence:', error);
      }
    }
  };

  // Fonction pour changer le mode
  const handleModeChange = async (newMode) => {
    setMode(newMode);

    if (connectionStatus === 'connected') {
      try {
        await axios.post(`${API_BASE_URL}/mode`, { mode: newMode });
        console.log(`Mode changé: ${newMode}`);
      } catch (error) {
        console.error('Erreur lors du changement de mode:', error);
      }
    }
  };

  // Fonction pour contrôler l'alimentation
  const handlePowerToggle = async () => {
    const newState = !isOn;
    setIsOn(newState);

    if (newState) {
      await startAudioStream();
    } else {
      stopAudioStream();
    }

    if (connectionStatus === 'connected') {
      try {
        await axios.post(`${API_BASE_URL}/power`, { state: newState });
        console.log(`Alimentation: ${newState ? 'ON' : 'OFF'}`);
      } catch (error) {
        console.error('Erreur lors du contrôle de l\'alimentation:', error);
      }
    }
  };

  // Fonction pour contrôler le volume
  const handleVolumeChange = async (newVolume) => {
    setVolume(newVolume);

    if (audioElementRef.current) {
      audioElementRef.current.volume = newVolume / 100;
    }

    if (connectionStatus === 'connected') {
      try {
        await axios.post(`${API_BASE_URL}/volume`, { volume: newVolume });
      } catch (error) {
        console.error('Erreur lors du changement de volume:', error);
      }
    }
  };

  // Fonction pour contrôler le squelch
  const handleSquelchChange = async (newSquelch) => {
    setSquelch(newSquelch);

    if (connectionStatus === 'connected') {
      try {
        await axios.post(`${API_BASE_URL}/squelch`, { squelch: newSquelch });
      } catch (error) {
        console.error('Erreur lors du changement de squelch:', error);
      }
    }
  };

  // Fonction pour sélectionner une fréquence tactique
  const selectTacticalFrequency = (freq) => {
    handleFrequencyChange(freq);
    setTacticalFrequencies(prev =>
      prev.map(f => ({ ...f, active: f.freq === freq }))
    );
  };

  // Fonction pour démarrer/arrêter l'enregistrement
  const toggleRecording = async () => {
    const newState = !isRecording;
    setIsRecording(newState);

    if (connectionStatus === 'connected') {
      try {
        await axios.post(`${API_BASE_URL}/record`, { recording: newState });
        console.log(`Enregistrement: ${newState ? 'DÉMARRÉ' : 'ARRÊTÉ'}`);
      } catch (error) {
        console.error('Erreur lors du contrôle de l\'enregistrement:', error);
      }
    }
  };

  // Fonction pour contrôler le streaming live
  const toggleLive = async () => {
    const newState = !isLive;
    setIsLive(newState);

    if (connectionStatus === 'connected') {
      try {
        await axios.post(`${API_BASE_URL}/live`, { live: newState });
        console.log(`Live: ${newState ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`);
      } catch (error) {
        console.error('Erreur lors du contrôle du live:', error);
      }
    }
  };

  // Fonction pour démarrer/arrêter le scan
  const toggleScan = async () => {
    const newScanState = !scanSettings.isScanning;
    setScanSettings(prev => ({ ...prev, isScanning: newScanState }));

    if (connectionStatus === 'connected') {
      try {
        if (newScanState) {
          await axios.post(`${API_BASE_URL}/scan/start`, {
            start_freq: scanSettings.startFreq,
            end_freq: scanSettings.endFreq,
            step: scanSettings.step
          });
        } else {
          await axios.post(`${API_BASE_URL}/scan/stop`);
        }
        console.log(`Scan: ${newScanState ? 'DÉMARRÉ' : 'ARRÊTÉ'}`);
      } catch (error) {
        console.error('Erreur lors du contrôle du scan:', error);
      }
    }
  };

  // Rendu de l'interface mini
  if (isMini) {
    return (
      <div
        className="fixed top-4 left-4 z-50 bg-gray-900 border border-green-500 rounded-lg p-4 shadow-2xl"
        style={{
          animation: 'fadeInScale 0.3s ease-out',
          minWidth: '300px'
        }}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isOn ? 'bg-green-500' : 'bg-red-500'}`}
              style={{ animation: isOn ? 'pulseGlow 2s infinite' : 'none' }} />
            <span className="text-green-400 font-bold text-sm">ICOM R8600</span>
          </div>
          <button
            onClick={() => setIsMini(false)}
            className="text-green-400 hover:text-green-300 transition-colors"
          >
            <Maximize className="w-4 h-4" />
          </button>
        </div>

        <div className="text-center">
          <div className="text-green-400 font-mono text-lg font-bold mb-1">
            {frequency.toFixed(3)}
          </div>
          <div className="text-green-300 text-xs mb-2">{mode}</div>

          <div className="flex items-center justify-center space-x-2 text-xs">
            <span className="text-green-400">AF: {afGain}%</span>
            <span className="text-green-400">RF: {rfGain}%</span>
          </div>

          {isReceiving && (
            <div className="text-green-400 text-xs mt-1 animate-pulse">
              ● RÉCEPTION
            </div>
          )}
        </div>
      </div>
    );
  }

  // Interface principale
  return (
    <div className="w-full h-full bg-gray-900 text-green-400 font-mono overflow-hidden">
      {/* En-tête */}
      <div className="bg-gray-800 border-b border-green-500 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Radio className="w-6 h-6 text-green-400" />
            <span className="text-green-400 font-bold text-lg">ICOM IC-R8600</span>
          </div>

          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isOn ? 'bg-green-500' : 'bg-red-500'}`}
              style={{ animation: isOn ? 'pulseGlow 2s infinite' : 'none' }} />
            <span className="text-sm">
              {isOn ? 'ON' : 'OFF'}
            </span>
          </div>

          <div className="text-sm text-green-300">
            STATION GUERRE ÉLECTRONIQUE
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsMini(true)}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors"
            title="Mode mini"
          >
            <Minimize className="w-4 h-4" />
          </button>

          <button
            onClick={handlePowerToggle}
            className={`p-2 rounded transition-colors ${isOn
                ? 'bg-red-600 hover:bg-red-500 text-white'
                : 'bg-green-600 hover:bg-green-500 text-white'
              }`}
          >
            <Power className="w-4 h-4" />
          </button>
        </div>
      </div>

      {!isOn ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <Power className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">Équipement hors tension</p>
            <p className="text-gray-600 text-sm mt-2">Cliquez sur le bouton d'alimentation pour démarrer</p>
          </div>
        </div>
      ) : (
        <div className="flex h-full">
          {/* Panneau de contrôles principaux */}
          <div className="w-1/3 bg-gray-800 border-r border-green-500 p-4 overflow-y-auto">
            <h3 className="text-green-400 font-bold mb-4 border-b border-green-500 pb-2">
              CONTRÔLES PRINCIPAUX
            </h3>

            {/* Fréquence */}
            <div className="mb-6">
              <label className="block text-green-400 text-sm font-bold mb-2">
                FRÉQUENCE (MHz)
              </label>
              <div className="bg-black border border-green-500 rounded p-4 text-center">
                <div className="text-green-400 font-mono text-3xl font-bold mb-2">
                  {frequency.toFixed(3)}
                </div>
                <input
                  type="range"
                  min="0.1"
                  max="3000"
                  step="0.001"
                  value={frequency}
                  onChange={(e) => handleFrequencyChange(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-green"
                />
                <div className="flex justify-between mt-2">
                  <button
                    onClick={() => handleFrequencyChange(Math.max(0.1, frequency - 0.001))}
                    className="bg-gray-700 hover:bg-gray-600 text-green-400 px-3 py-1 rounded text-sm"
                  >
                    -
                  </button>
                  <button
                    onClick={() => handleFrequencyChange(Math.min(3000, frequency + 0.001))}
                    className="bg-gray-700 hover:bg-gray-600 text-green-400 px-3 py-1 rounded text-sm"
                  >
                    +
                  </button>
                </div>
              </div>
            </div>

            {/* Mode et Filtres */}
            <div className="mb-6">
              <label className="block text-green-400 text-sm font-bold mb-2">
                MODE
              </label>
              <select
                value={mode}
                onChange={(e) => handleModeChange(e.target.value)}
                className="w-full bg-gray-700 border border-green-500 text-green-400 rounded p-2"
              >
                <option value="FM">FM</option>
                <option value="WFM">WFM</option>
                <option value="AM">AM</option>
                <option value="USB">USB</option>
                <option value="LSB">LSB</option>
                <option value="CW">CW</option>
                <option value="FSK">FSK</option>
                <option value="DIGITAL">DIGITAL</option>
              </select>

              <label className="block text-green-400 text-sm font-bold mt-4 mb-2">
                FILTRES
              </label>
              <div className="flex space-x-2">
                <select className="flex-1 bg-gray-700 border border-green-500 text-green-400 rounded p-2">
                  <option value="WIDE">WIDE</option>
                  <option value="NARROW">NARROW</option>
                  <option value="MEDIUM">MEDIUM</option>
                </select>
              </div>
            </div>

            {/* Gains */}
            <div className="mb-6">
              <label className="block text-green-400 text-sm font-bold mb-2">
                AF GAIN: {afGain}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={afGain}
                onChange={(e) => setAfGain(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-green"
              />

              <label className="block text-green-400 text-sm font-bold mt-4 mb-2">
                RF GAIN: {rfGain}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={rfGain}
                onChange={(e) => setRfGain(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-green"
              />
            </div>

            {/* Molette de fréquence */}
            <div className="mb-6">
              <label className="block text-green-400 text-sm font-bold mb-2 text-center">
                MOLETTE FRÉQ
              </label>
              <div className="flex justify-center">
                <div className="relative w-32 h-32 bg-gray-700 rounded-full border-4 border-green-500 flex items-center justify-center">
                  <div className="w-2 h-8 bg-green-400 rounded-full transform -translate-y-8"></div>
                  <div className="absolute text-green-400 text-xs bottom-2">
                    ±10Hz
                  </div>
                </div>
              </div>
            </div>

            {/* Modulation et Fréquence */}
            <div className="mb-6">
              <h4 className="text-green-400 font-bold mb-2">MODULATION & FRÉQUENCE</h4>
              <div className="grid grid-cols-4 gap-2 mb-4">
                {['FM', 'WFM', 'AM', 'USB', 'LSB', 'CW', 'FSK', 'DIGITAL'].map((m) => (
                  <button
                    key={m}
                    onClick={() => handleModeChange(m)}
                    className={`p-2 rounded text-xs font-bold transition-colors ${mode === m
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-700 text-green-400 hover:bg-gray-600'
                      }`}
                  >
                    {m}
                  </button>
                ))}
              </div>

              <div className="grid grid-cols-3 gap-2">
                {['-100K', '-25K', '-10K', '+10K', '+25K', '+100K'].map((step) => (
                  <button
                    key={step}
                    onClick={() => {
                      const stepValue = parseInt(step.replace('K', '')) * 0.001;
                      handleFrequencyChange(frequency + stepValue);
                    }}
                    className="bg-gray-700 hover:bg-gray-600 text-green-400 p-2 rounded text-xs font-bold"
                  >
                    {step}
                  </button>
                ))}
              </div>
            </div>

            {/* Audio Live Récepteur */}
            <div className="mb-6">
              <h4 className="text-green-400 font-bold mb-2">AUDIO LIVE RÉCEPTEUR</h4>

              <div className="mb-4">
                <label className="block text-green-400 text-sm font-bold mb-2">
                  AF OUTPUT LEVEL: {audioLevel}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={audioLevel * 10}
                  onChange={(e) => setAudioLevel(parseInt(e.target.value) / 10)}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-green"
                />
              </div>

              <div className="flex items-center justify-between mb-4">
                <span className="text-green-400 font-bold">Audio Live: ARRÊTÉ</span>
                <button
                  onClick={toggleLive}
                  className={`px-4 py-2 rounded font-bold transition-colors ${isLive
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-700 text-green-400 hover:bg-gray-600'
                    }`}
                >
                  {isLive ? '?? LIVE' : '?? LIVE'}
                </button>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-green-400">??? ENREGISTREMENT</span>
                <button
                  onClick={toggleRecording}
                  className={`px-4 py-2 rounded font-bold transition-colors ${isRecording
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-700 text-green-400 hover:bg-gray-600'
                    }`}
                >
                  {isRecording ? '??? REC' : '??? REC'}
                </button>
              </div>
            </div>
          </div>

          {/* Panneau central - Fréquences tactiques */}
          <div className="w-1/3 bg-gray-800 border-r border-green-500 p-4 overflow-y-auto">
            <h3 className="text-green-400 font-bold mb-4 border-b border-green-500 pb-2">
              FRÉQUENCES TACTIQUES
            </h3>

            <div className="space-y-2 max-h-80 overflow-y-auto">
              {tacticalFrequencies.map((freq) => (
                <div
                  key={freq.id}
                  onClick={() => selectTacticalFrequency(freq.freq)}
                  className={`p-3 rounded border cursor-pointer transition-colors ${freq.active
                      ? 'bg-green-600 border-green-400 text-white'
                      : 'bg-gray-700 border-green-500 text-green-400 hover:bg-gray-600'
                    }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-bold">{freq.name}</div>
                      <div className="text-sm">{freq.freq.toFixed(3)} MHz</div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs">MODE: FM</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Scan programmé */}
            <div className="mt-6">
              <h4 className="text-green-400 font-bold mb-4 border-b border-green-500 pb-2">
                ?? PROGRAMMED SCAN CI-V COMPLET
              </h4>

              <div className="space-y-4">
                <div>
                  <label className="block text-green-400 text-sm font-bold mb-1">
                    DÉBUT (Hz)
                  </label>
                  <input
                    type="number"
                    value={scanSettings.startFreq}
                    onChange={(e) => setScanSettings(prev => ({ ...prev, startFreq: parseInt(e.target.value) }))}
                    className="w-full bg-gray-700 border border-green-500 text-green-400 rounded p-2"
                  />
                </div>

                <div>
                  <label className="block text-green-400 text-sm font-bold mb-1">
                    FIN (Hz)
                  </label>
                  <input
                    type="number"
                    value={scanSettings.endFreq}
                    onChange={(e) => setScanSettings(prev => ({ ...prev, endFreq: parseInt(e.target.value) }))}
                    className="w-full bg-gray-700 border border-green-500 text-green-400 rounded p-2"
                  />
                </div>

                <div>
                  <label className="block text-green-400 text-sm font-bold mb-1">
                    PAS DE FRÉQUENCE (Hz)
                  </label>
                  <input
                    type="number"
                    value={scanSettings.step}
                    onChange={(e) => setScanSettings(prev => ({ ...prev, step: parseInt(e.target.value) }))}
                    className="w-full bg-gray-700 border border-green-500 text-green-400 rounded p-2"
                  />
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={toggleScan}
                    className={`flex-1 py-2 rounded font-bold transition-colors ${scanSettings.isScanning
                        ? 'bg-red-600 text-white'
                        : 'bg-green-600 text-white hover:bg-green-500'
                      }`}
                  >
                    {scanSettings.isScanning ? '?? LANCER' : '?? LANCER'}
                  </button>

                  <button
                    onClick={() => setScanSettings(prev => ({ ...prev, isScanning: false }))}
                    className="flex-1 bg-red-600 hover:bg-red-500 text-white py-2 rounded font-bold"
                  >
                    ?? ARRÊTER
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Panneau droit - Audio et analyse */}
          <div className="w-1/3 bg-gray-800 p-4 overflow-y-auto">
            <h3 className="text-green-400 font-bold mb-4 border-b border-green-500 pb-2">
              AUDIO & ANALYSE
            </h3>

            {/* Indicateurs de signal */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-green-400 text-sm">Signal:</span>
                <span className="text-green-400 font-bold">-69.7 dBm</span>
              </div>

              <div className="w-full bg-gray-700 rounded-full h-4 mb-4">
                <div
                  className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-4 rounded-full transition-all duration-300"
                  style={{ width: `${signalStrength}%` }}
                ></div>
              </div>

              <div className="grid grid-cols-5 gap-1 mb-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className={`h-8 rounded ${i <= Math.floor(signalStrength / 20)
                        ? 'bg-green-500'
                        : 'bg-gray-600'
                      }`}
                  ></div>
                ))}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-green-400 text-sm">Volume:</span>
                <span className="text-green-400 font-bold">{volume}%</span>
              </div>

              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-green mt-2"
              />
            </div>

            {/* Squelch */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-green-400 text-sm">Squelch:</span>
                <span className="text-green-400 font-bold">{squelch}</span>
              </div>

              <input
                type="range"
                min="0"
                max="100"
                value={squelch}
                onChange={(e) => handleSquelchChange(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-green"
              />
            </div>

            {/* Niveau audio */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-green-400 text-sm">Audio Level:</span>
                <span className="text-green-400 font-bold">{audioLevel}%</span>
              </div>

              <div className="w-full bg-gray-700 rounded-full h-4">
                <div
                  className="bg-green-500 h-4 rounded-full transition-all duration-300"
                  style={{ width: `${audioLevel * 10}%` }}
                ></div>
              </div>
            </div>

            {/* Contrôles d'enregistrement */}
            <div className="mb-6">
              <div className="flex items-center justify-center mb-4">
                <button
                  onClick={toggleRecording}
                  className={`p-3 rounded-full transition-colors ${isRecording
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-700 text-green-400 hover:bg-gray-600'
                    }`}
                >
                  {isRecording ? <Square className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>
              </div>

              <div className="text-center">
                <span className="text-green-400 text-sm">
                  {isRecording ? 'REC' : 'STANDBY'}
                </span>
              </div>
            </div>

            {/* Alimentation */}
            <div className="mb-6">
              <h4 className="text-green-400 font-bold mb-2">Alimentation:</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-400">Préenregistrement:</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-400">Streaming:</span>
                </div>
              </div>
            </div>

            {/* Position */}
            <div className="mt-auto pt-4 border-t border-green-500">
              <div className="flex items-center justify-between text-sm">
                <span className="text-green-400">Position</span>
                <span className="text-green-300 italic">Survolez la carte pour voir les coordonnées</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default C2EWRadioControl;
