import React, { useState, useEffect } from 'react';
import './c2ew-styles.css';

// Import seulement les icônes qui existent vraiment dans lucide-react
import {
  Power,
  Radio,
  Volume2,
  Settings,
  Play,
  Square,
  Mic,
  MicOff,
  Plus,
  Minus,
  Headphones,
  Maximize,
  Minimize
} from 'lucide-react';

const C2EWRadioControl = () => {
  // États principaux
  const [isOn, setIsOn] = useState(false);
  const [isMini, setIsMini] = useState(false);
  const [frequency, setFrequency] = useState(7500.120);
  const [mode, setMode] = useState('FM');
  const [volume, setVolume] = useState(50);
  const [squelch, setSquelch] = useState(0);
  const [afGain, setAfGain] = useState(50);
  const [rfGain, setRfGain] = useState(50);
  const [audioLevel, setAudioLevel] = useState(5);
  const [isRecording, setIsRecording] = useState(false);
  const [isLive, setIsLive] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [signalStrength, setSignalStrength] = useState(0);

  // Fréquences tactiques prédéfinies
  const [tacticalFrequencies] = useState([
    { id: 1, name: 'Canal d\'urgence principal', freq: 145.500, active: true },
    { id: 2, name: 'Coordination trafic', freq: 145.750, active: false },
    { id: 3, name: 'Canal de coordination', freq: 146.000, active: false },
    { id: 4, name: 'UHF Principal', freq: 433.500, active: false },
    { id: 5, name: 'ISM Band', freq: 868.000, active: false }
  ]);

  // Configuration de l'API
  const API_BASE_URL = 'http://localhost:8100';

  // Effet pour vérifier la connexion au backend iCOM
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/health`);
        if (response.ok) {
          setConnectionStatus('connected');
          console.log('✅ Connexion iCOM établie');
        } else {
          setConnectionStatus('disconnected');
        }
      } catch (error) {
        setConnectionStatus('disconnected');
        console.log('❌ Backend iCOM non disponible');
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 5000);

    return () => clearInterval(interval);
  }, []);

  // Effet pour la simulation des métriques
  useEffect(() => {
    if (!isOn) return;

    const interval = setInterval(() => {
      setSignalStrength(Math.floor(Math.random() * 100));
    }, 1000);

    return () => clearInterval(interval);
  }, [isOn]);

  // Fonction pour contrôler l'alimentation
  const handlePowerToggle = () => {
    setIsOn(!isOn);
  };

  // Fonction pour changer la fréquence
  const handleFrequencyChange = (newFreq) => {
    setFrequency(newFreq);
  };

  // Fonction pour changer le mode
  const handleModeChange = (newMode) => {
    setMode(newMode);
  };

  // Fonction pour contrôler le volume
  const handleVolumeChange = (newVolume) => {
    setVolume(newVolume);
  };

  // Fonction pour contrôler le squelch
  const handleSquelchChange = (newSquelch) => {
    setSquelch(newSquelch);
  };

  // Fonction pour sélectionner une fréquence tactique
  const selectTacticalFrequency = (freq) => {
    handleFrequencyChange(freq);
  };

  // Fonction pour démarrer/arrêter l'enregistrement
  const toggleRecording = () => {
    setIsRecording(!isRecording);
  };

  // Fonction pour contrôler le streaming live
  const toggleLive = () => {
    setIsLive(!isLive);
  };

  // Rendu de l'interface mini
  if (isMini) {
    return (
      <div className="fixed top-4 left-4 z-50 bg-gray-900 border border-green-500 rounded-lg p-4 shadow-2xl min-w-[300px]">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isOn ? 'bg-green-500' : 'bg-red-500'} animate-pulse`} />
            <span className="text-green-400 font-bold text-sm">ICOM R8600</span>
          </div>
          <button
            onClick={() => setIsMini(false)}
            className="text-green-400 hover:text-green-300 transition-colors"
          >
            <Maximize className="w-4 h-4" />
          </button>
        </div>

        <div className="text-center">
          <div className="text-green-400 font-mono text-lg font-bold mb-1">
            {frequency.toFixed(3)}
          </div>
          <div className="text-green-300 text-xs mb-2">{mode}</div>

          <div className="flex items-center justify-center space-x-2 text-xs">
            <span className="text-green-400">AF: {afGain}%</span>
            <span className="text-green-400">RF: {rfGain}%</span>
          </div>
        </div>
      </div>
    );
  }

  // Interface principale
  return (
    <div className="w-full h-full bg-gray-900 text-green-400 font-mono overflow-hidden">
      {/* En-tête */}
      <div className="bg-gray-800 border-b border-green-500 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Radio className="w-6 h-6 text-green-400" />
            <span className="text-green-400 font-bold text-lg">ICOM IC-R8600</span>
          </div>

          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isOn ? 'bg-green-500' : 'bg-red-500'} animate-pulse`} />
            <span className="text-sm">
              {isOn ? 'ON' : 'OFF'}
            </span>
          </div>

          <div className="text-sm text-green-300">
            STATION GUERRE ÉLECTRONIQUE
          </div>

          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${connectionStatus === 'connected' ? 'bg-green-500' : 'bg-red-500'
              } animate-pulse`} />
            <span className="text-xs">
              {connectionStatus === 'connected' ? 'CONNECTÉ' : 'DÉCONNECTÉ'}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsMini(true)}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors"
            title="Mode mini"
          >
            <Minimize className="w-4 h-4" />
          </button>

          <button
            onClick={handlePowerToggle}
            className={`p-2 rounded transition-colors ${isOn
              ? 'bg-red-600 hover:bg-red-500 text-white'
              : 'bg-green-600 hover:bg-green-500 text-white'
              }`}
          >
            <Power className="w-4 h-4" />
          </button>
        </div>
      </div>

      {!isOn ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <Power className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">Équipement hors tension</p>
            <p className="text-gray-600 text-sm mt-2">Cliquez sur le bouton d'alimentation pour démarrer</p>
          </div>
        </div>
      ) : (
        <div className="flex h-full">
          {/* Panneau de contrôles principaux */}
          <div className="w-1/3 bg-gray-800 border-r border-green-500 p-4 overflow-y-auto">
            <h3 className="text-green-400 font-bold mb-4 border-b border-green-500 pb-2">
              CONTRÔLES PRINCIPAUX
            </h3>

            {/* Fréquence */}
            <div className="mb-6">
              <label className="block text-green-400 text-sm font-bold mb-2">
                FRÉQUENCE (MHz)
              </label>
              <div className="bg-black border border-green-500 rounded p-4 text-center">
                <div className="text-green-400 font-mono text-3xl font-bold mb-2">
                  {frequency.toFixed(3)}
                </div>
                <input
                  type="range"
                  min="0.1"
                  max="3000"
                  step="0.001"
                  value={frequency}
                  onChange={(e) => handleFrequencyChange(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between mt-2">
                  <button
                    onClick={() => handleFrequencyChange(Math.max(0.1, frequency - 0.001))}
                    className="bg-gray-700 hover:bg-gray-600 text-green-400 px-3 py-1 rounded text-sm"
                  >
                    <Minus className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => handleFrequencyChange(Math.min(3000, frequency + 0.001))}
                    className="bg-gray-700 hover:bg-gray-600 text-green-400 px-3 py-1 rounded text-sm"
                  >
                    <Plus className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>

            {/* Mode */}
            <div className="mb-6">
              <label className="block text-green-400 text-sm font-bold mb-2">
                MODE
              </label>
              <select
                value={mode}
                onChange={(e) => handleModeChange(e.target.value)}
                className="w-full bg-gray-700 border border-green-500 text-green-400 rounded p-2"
              >
                <option value="FM">FM</option>
                <option value="WFM">WFM</option>
                <option value="AM">AM</option>
                <option value="USB">USB</option>
                <option value="LSB">LSB</option>
                <option value="CW">CW</option>
                <option value="FSK">FSK</option>
                <option value="DIGITAL">DIGITAL</option>
              </select>
            </div>

            {/* Gains */}
            <div className="mb-6">
              <label className="block text-green-400 text-sm font-bold mb-2">
                AF GAIN: {afGain}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={afGain}
                onChange={(e) => setAfGain(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />

              <label className="block text-green-400 text-sm font-bold mt-4 mb-2">
                RF GAIN: {rfGain}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={rfGain}
                onChange={(e) => setRfGain(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>

          {/* Panneau central - Fréquences tactiques */}
          <div className="w-1/3 bg-gray-800 border-r border-green-500 p-4 overflow-y-auto">
            <h3 className="text-green-400 font-bold mb-4 border-b border-green-500 pb-2">
              FRÉQUENCES TACTIQUES
            </h3>

            <div className="space-y-2 max-h-80 overflow-y-auto">
              {tacticalFrequencies.map((freq) => (
                <div
                  key={freq.id}
                  onClick={() => selectTacticalFrequency(freq.freq)}
                  className={`p-3 rounded border cursor-pointer transition-colors ${freq.active
                    ? 'bg-green-600 border-green-400 text-white'
                    : 'bg-gray-700 border-green-500 text-green-400 hover:bg-gray-600'
                    }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-bold">{freq.name}</div>
                      <div className="text-sm">{freq.freq.toFixed(3)} MHz</div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs">MODE: FM</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Panneau droit - Audio et analyse */}
          <div className="w-1/3 bg-gray-800 p-4 overflow-y-auto">
            <h3 className="text-green-400 font-bold mb-4 border-b border-green-500 pb-2">
              AUDIO & ANALYSE
            </h3>

            {/* Indicateurs de signal */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-green-400 text-sm">Signal:</span>
                <span className="text-green-400 font-bold">-69.7 dBm</span>
              </div>

              <div className="w-full bg-gray-700 rounded-full h-4 mb-4">
                <div
                  className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-4 rounded-full transition-all duration-300"
                  style={{ width: `${signalStrength}%` }}
                ></div>
              </div>

              <div className="grid grid-cols-5 gap-1 mb-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className={`h-8 rounded ${i <= Math.floor(signalStrength / 20)
                      ? 'bg-green-500'
                      : 'bg-gray-600'
                      }`}
                  ></div>
                ))}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-green-400 text-sm">Volume:</span>
                <span className="text-green-400 font-bold">{volume}%</span>
              </div>

              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer mt-2"
              />
            </div>

            {/* Squelch */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-green-400 text-sm">Squelch:</span>
                <span className="text-green-400 font-bold">{squelch}</span>
              </div>

              <input
                type="range"
                min="0"
                max="100"
                value={squelch}
                onChange={(e) => handleSquelchChange(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Niveau audio */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-green-400 text-sm">Audio Level:</span>
                <span className="text-green-400 font-bold">{audioLevel}%</span>
              </div>

              <div className="w-full bg-gray-700 rounded-full h-4">
                <div
                  className="bg-green-500 h-4 rounded-full transition-all duration-300"
                  style={{ width: `${audioLevel * 10}%` }}
                ></div>
              </div>
            </div>

            {/* Contrôles d'enregistrement */}
            <div className="mb-6">
              <div className="flex items-center justify-center mb-4">
                <button
                  onClick={toggleRecording}
                  className={`p-3 rounded-full transition-colors ${isRecording
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-700 text-green-400 hover:bg-gray-600'
                    }`}
                >
                  {isRecording ? <Square className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>
              </div>

              <div className="text-center">
                <span className="text-green-400 text-sm">
                  {isRecording ? 'REC' : 'STANDBY'}
                </span>
              </div>
            </div>

            {/* Position */}
            <div className="mt-auto pt-4 border-t border-green-500">
              <div className="flex items-center justify-between text-sm">
                <span className="text-green-400">Position</span>
                <span className="text-green-300 italic">Survolez la carte pour voir les coordonnées</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default C2EWRadioControl;
