import React, { useState, useEffect } from 'react';
import { ExternalLink, Maximize2, Minimize2, RefreshCw } from 'lucide-react';

const ICOMIntegratedInterface = ({ equipment, onClose }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [iframeKey, setIframeKey] = useState(0);

  // URL de l'interface iCOM réelle
  const ICOM_INTERFACE_URL = 'http://localhost:5173';

  useEffect(() => {
    // Vérifier que l'interface iCOM est disponible
    const checkICOMInterface = async () => {
      try {
        const response = await fetch(ICOM_INTERFACE_URL);
        if (response.ok) {
          console.log('✅ Interface iCOM disponible');
        } else {
          console.log('❌ Interface iCOM non disponible');
        }
      } catch (error) {
        console.log('❌ Interface iCOM non accessible:', error);
      }
      setIsLoading(false);
    };

    checkICOMInterface();
  }, []);

  const handleRefresh = () => {
    setIsLoading(true);
    setIframeKey(prev => prev + 1);
  };

  const handleOpenExternal = () => {
    window.open(ICOM_INTERFACE_URL, '_blank');
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'w-full h-full'} bg-gray-900 text-white flex flex-col`}>
      {/* En-tête de contrôle */}
      <div className="bg-gray-800 border-b border-gray-600 p-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">Interface iCOM IC-R8600</span>
          <span className="text-xs text-gray-400">
            {equipment?.name || 'Station Radio'}
          </span>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors"
            title="Actualiser"
          >
            <RefreshCw className="w-4 h-4" />
          </button>

          <button
            onClick={handleOpenExternal}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors"
            title="Ouvrir dans un nouvel onglet"
          >
            <ExternalLink className="w-4 h-4" />
          </button>

          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors"
            title={isFullscreen ? "Réduire" : "Plein écran"}
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>

          {onClose && (
            <button
              onClick={onClose}
              className="p-2 bg-red-600 hover:bg-red-500 rounded transition-colors"
              title="Fermer"
            >
              ×
            </button>
          )}
        </div>
      </div>

      {/* Zone de contenu */}
      <div className="flex-1 relative">
        {isLoading && (
          <div className="absolute inset-0 bg-gray-900 flex items-center justify-center z-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
              <p className="text-gray-300">Chargement de l'interface iCOM...</p>
              <p className="text-xs text-gray-500 mt-2">
                Vérifiez que le frontend iCOM est lancé sur le port 5173
              </p>
            </div>
          </div>
        )}

        <iframe
          key={iframeKey}
          src={ICOM_INTERFACE_URL}
          className="w-full h-full border-0"
          onLoad={handleIframeLoad}
          onError={() => setIsLoading(false)}
          title="Interface iCOM IC-R8600"
          allow="microphone; camera; autoplay"
        />
      </div>

      {/* Pied de page avec informations */}
      <div className="bg-gray-800 border-t border-gray-600 p-2 text-xs text-gray-400">
        <div className="flex justify-between items-center">
          <span>Interface iCOM intégrée - Port 5173</span>
          <span>Backend: localhost:8100 | Audio: localhost:8102</span>
        </div>
      </div>
    </div>
  );
};

export default ICOMIntegratedInterface;
