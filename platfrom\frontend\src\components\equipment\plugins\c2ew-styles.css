/* Styles CSS pour l'interface C2-EW Platform */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto+Mono:wght@300;400;500;700&display=swap');

/* Variables CSS pour le thème C2-EW */
:root {
  --c2ew-primary: #000000;
  --c2ew-secondary: #111111;
  --c2ew-accent: #2563eb;
  --c2ew-success: #10b981;
  --c2ew-warning: #f59e0b;
  --c2ew-danger: #ef4444;
  --c2ew-dark: #000000;
  --c2ew-dark-card: #111111;
  --c2ew-dark-border: #333333;
  --c2ew-text: #ffffff;
  --c2ew-text-muted: #888888;
  --c2ew-lcd: #00ff41;
  --c2ew-lcd-bg: #000000;
}

/* Reset et base */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  background: var(--c2ew-dark);
  color: var(--c2ew-text);
  overflow-x: hidden;
}

/* Animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes glow {
  0% { text-shadow: 0 0 5px var(--c2ew-lcd); }
  50% { text-shadow: 0 0 20px var(--c2ew-lcd), 0 0 30px var(--c2ew-lcd); }
  100% { text-shadow: 0 0 5px var(--c2ew-lcd); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Classes utilitaires */
.pulse { animation: pulse 2s infinite; }
.glow { animation: glow 2s infinite; }
.rotate { animation: rotate 2s linear infinite; }
.slide-in { animation: slideIn 0.5s ease-out; }
.fade-in { animation: fadeIn 0.5s ease-out; }

/* Interface principale */
.c2ew-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--c2ew-dark) 0%, var(--c2ew-secondary) 100%);
  color: var(--c2ew-text);
  font-family: 'Roboto Mono', monospace;
}

/* Cartes et panneaux */
.c2ew-card {
  background: var(--c2ew-dark-card);
  border: 1px solid var(--c2ew-dark-border);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.c2ew-panel {
  background: linear-gradient(145deg, var(--c2ew-dark-card), var(--c2ew-dark));
  border: 1px solid var(--c2ew-dark-border);
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0.5rem 0;
}

/* Affichage LCD */
.c2ew-lcd {
  background: var(--c2ew-lcd-bg);
  color: var(--c2ew-lcd);
  font-family: 'Orbitron', 'Roboto Mono', monospace;
  font-weight: 700;
  text-align: center;
  padding: 0.5rem;
  border: 2px inset var(--c2ew-dark-border);
  border-radius: 4px;
  text-shadow: 0 0 10px var(--c2ew-lcd);
  letter-spacing: 0.1em;
}

.c2ew-lcd-large {
  font-size: 2rem;
  padding: 1rem;
}

.c2ew-lcd-medium {
  font-size: 1.5rem;
  padding: 0.75rem;
}

.c2ew-lcd-small {
  font-size: 1rem;
  padding: 0.5rem;
}

/* Boutons */
.c2ew-button {
  background: linear-gradient(145deg, #333333, #222222);
  border: 1px solid var(--c2ew-dark-border);
  color: var(--c2ew-text);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Roboto Mono', monospace;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.c2ew-button:hover {
  background: linear-gradient(145deg, #444444, #333333);
  box-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
}

.c2ew-button:active {
  transform: translateY(0);
  box-shadow: 0 0 5px rgba(37, 99, 235, 0.2);
}

.c2ew-button.active {
  background: linear-gradient(145deg, var(--c2ew-accent), #1d4ed8);
  color: white;
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.5);
}

.c2ew-button.danger {
  background: linear-gradient(145deg, var(--c2ew-danger), #dc2626);
  color: white;
}

.c2ew-button.success {
  background: linear-gradient(145deg, var(--c2ew-success), #059669);
  color: white;
}

.c2ew-button.warning {
  background: linear-gradient(145deg, var(--c2ew-warning), #d97706);
  color: white;
}

/* Inputs et contrôles */
.c2ew-input {
  background: var(--c2ew-dark);
  border: 1px solid var(--c2ew-dark-border);
  color: var(--c2ew-text);
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
  transition: border-color 0.2s ease;
}

.c2ew-input:focus {
  outline: none;
  border-color: var(--c2ew-accent);
  box-shadow: 0 0 5px rgba(37, 99, 235, 0.3);
}

.c2ew-select {
  background: var(--c2ew-dark);
  border: 1px solid var(--c2ew-dark-border);
  color: var(--c2ew-text);
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
}

/* Sliders personnalisés */
.c2ew-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  background: var(--c2ew-dark);
  border: 1px solid var(--c2ew-dark-border);
  border-radius: 3px;
  outline: none;
}

.c2ew-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--c2ew-accent);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(37, 99, 235, 0.5);
}

.c2ew-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--c2ew-accent);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(37, 99, 235, 0.5);
}

/* Barres de progression et indicateurs */
.c2ew-progress {
  width: 100%;
  height: 20px;
  background: var(--c2ew-dark);
  border: 1px solid var(--c2ew-dark-border);
  border-radius: 10px;
  overflow: hidden;
}

.c2ew-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--c2ew-success), var(--c2ew-warning), var(--c2ew-danger));
  transition: width 0.3s ease;
  border-radius: 10px;
}

/* Indicateurs de signal */
.c2ew-signal-bars {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 20px;
}

.c2ew-signal-bar {
  width: 4px;
  background: var(--c2ew-dark-border);
  border-radius: 2px;
  transition: background-color 0.3s ease;
}

.c2ew-signal-bar.active {
  background: var(--c2ew-success);
  box-shadow: 0 0 5px var(--c2ew-success);
}

/* Textes et typographie */
.c2ew-text-primary { color: var(--c2ew-text); }
.c2ew-text-muted { color: var(--c2ew-text-muted); }
.c2ew-text-accent { color: var(--c2ew-accent); }
.c2ew-text-success { color: var(--c2ew-success); }
.c2ew-text-warning { color: var(--c2ew-warning); }
.c2ew-text-danger { color: var(--c2ew-danger); }
.c2ew-text-lcd { color: var(--c2ew-lcd); }

.c2ew-font-mono { font-family: 'Roboto Mono', monospace; }
.c2ew-font-orbitron { font-family: 'Orbitron', monospace; }

/* États et statuts */
.c2ew-status-online {
  color: var(--c2ew-success);
  animation: pulse 2s infinite;
}

.c2ew-status-offline {
  color: var(--c2ew-danger);
}

.c2ew-status-standby {
  color: var(--c2ew-warning);
}

/* Responsive */
@media (max-width: 768px) {
  .c2ew-container {
    padding: 0.5rem;
  }
  
  .c2ew-card {
    padding: 0.75rem;
  }
  
  .c2ew-panel {
    padding: 0.5rem;
  }
  
  .c2ew-lcd-large {
    font-size: 1.5rem;
  }
  
  .c2ew-lcd-medium {
    font-size: 1.25rem;
  }
}

/* Scrollbars personnalisées */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--c2ew-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--c2ew-dark-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--c2ew-accent);
}
