import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Radio,
  Radar,
  Shield,
  Zap,
  ChevronRight,
  ChevronDown,
  Circle,
  Square,
  Ruler,
  MapPin,
  Pencil,
  Eye,
  Menu,
  X,
  Target,
  Settings,
  Plus
} from 'lucide-react';
import { useEquipmentStore } from '@/store/equipmentStore';
import { useMapStore } from '@/store/mapStore';
import { EQUIPMENT_TYPES, STATUS_COLORS, LAYOUT } from '@/utils/constants';
import EquipmentManager from '@/components/equipment/EquipmentManager';

const SidebarLeftModern = ({ isOpen, onToggle, onEquipmentSelect }) => {
  const [expandedCategories, setExpandedCategories] = useState({
    comint: true,
    elint: false,
    anti_drone: false,
    jammer: false,
    tactical: false,
    markers: false,
    equipment_manager: false,
  });
  const [hovering, setHovering] = useState(false);
  const [activeMapTool, setActiveMapTool] = useState(null);
  const [showEquipmentManager, setShowEquipmentManager] = useState(false);

  const { equipmentByType, getEquipmentByType, integratedEquipment, loadIntegratedEquipment } = useEquipmentStore();
  const { setActiveTool } = useMapStore();

  useEffect(() => {
    // Charger les équipements par type (mockés)
    Object.keys(EQUIPMENT_TYPES).forEach(type => {
      getEquipmentByType(type.toLowerCase());
    });

    // Charger les équipements intégrés
    loadIntegratedEquipment().then(integrated => {
      console.log('🔧 Équipements intégrés chargés:', integrated);
    });
  }, [getEquipmentByType, loadIntegratedEquipment]);

  const toggleCategory = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const handleEquipmentClick = (equipment) => {
    onEquipmentSelect(equipment.id);
  };

  const handleTacticalTool = (tool) => {
    if (activeMapTool === tool) {
      // Désactiver l'outil si déjà actif
      setActiveMapTool(null);
      setActiveTool(null);
    } else {
      // Activer le nouvel outil
      setActiveMapTool(tool);
      setActiveTool(tool);
    }
  };

  const getEquipmentIcon = (type) => {
    switch (type) {
      case 'comint': return Radio;
      case 'elint': return Radar;
      case 'anti_drone': return Shield;
      case 'jammer': return Zap;
      default: return Radio;
    }
  };

  const getStatusIndicator = (status) => {
    const color = STATUS_COLORS[status] || STATUS_COLORS.unknown;
    return (
      <div
        className={`w-2 h-2 rounded-full`}
        style={{ backgroundColor: color }}
      />
    );
  };

  // Outils tactiques modernes
  const tacticalTools = [
    { id: 'distance', name: 'Mesure Distance', icon: Ruler, category: 'measure' },
    { id: 'trajectory', name: 'Trajectoire', icon: Pencil, category: 'measure' },
    { id: 'visibility', name: 'Visibilité Optique', icon: Eye, category: 'analysis' },
    { id: 'circle', name: 'Zone Circulaire', icon: Circle, category: 'zone' },
    { id: 'polygon', name: 'Zone Polygonale', icon: Square, category: 'zone' },
    { id: 'marker', name: 'Marqueur', icon: MapPin, category: 'point' },
  ];

  const shouldShowExpanded = isOpen || hovering;

  return (
    <>
      {/* Sidebar avec barre de défilement */}
      <motion.div
        className={`fixed left-0 top-16 h-full bg-c2-gray-400 border-r border-c2-gray-300 z-40 ${shouldShowExpanded ? 'shadow-lg' : ''
          }`}
        style={{
          width: shouldShowExpanded ? LAYOUT.SIDEBAR_WIDTH : LAYOUT.SIDEBAR_COLLAPSED_WIDTH,
        }}
        animate={{
          width: shouldShowExpanded ? LAYOUT.SIDEBAR_WIDTH : LAYOUT.SIDEBAR_COLLAPSED_WIDTH,
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
      >
        {/* Bouton toggle */}
        <div className="absolute -right-3 top-4 z-50">
          <button
            onClick={() => onToggle(!isOpen)}
            className="w-6 h-6 bg-c2-gray-300 border border-c2-gray-200 rounded-full flex items-center justify-center hover:bg-c2-gray-200 transition-colors"
          >
            {isOpen ? (
              <X className="w-3 h-3 text-c2-white" />
            ) : (
              <Menu className="w-3 h-3 text-c2-white" />
            )}
          </button>
        </div>

        {/* Contenu de la sidebar */}
        <AnimatePresence>
          {shouldShowExpanded && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="h-full flex flex-col overflow-hidden"
            >
              {/* Header */}
              <div className="p-4 border-b border-c2-gray-300">
                <h2 className="text-lg font-semibold text-c2-white">Équipements</h2>
              </div>

              {/* Contenu scrollable avec barre de défilement */}
              <div
                className="flex-1 overflow-y-auto pr-2"
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#68d391 #2d3748',
                  maxHeight: 'calc(100vh - 200px)'
                }}
              >

                {/* GESTION D'ÉQUIPEMENTS */}
                <div className="border-b border-c2-gray-300">
                  <button
                    onClick={() => toggleCategory('equipment_manager')}
                    className="w-full p-3 flex items-center justify-between hover:bg-c2-gray-300 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Settings className="w-5 h-5 text-c2-blue" />
                      <span className="text-sm font-medium text-c2-white">Gestion d'Équipements</span>
                    </div>
                    {expandedCategories.equipment_manager ? (
                      <ChevronDown className="w-4 h-4 text-c2-gray-100" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-c2-gray-100" />
                    )}
                  </button>

                  <AnimatePresence>
                    {expandedCategories.equipment_manager && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="overflow-hidden"
                      >
                        <div className="px-4 pb-2 space-y-2">
                          <button
                            onClick={() => setShowEquipmentManager(true)}
                            className="w-full p-2 flex items-center space-x-2 hover:bg-c2-gray-300 rounded transition-colors text-left"
                          >
                            <Plus className="w-4 h-4 text-c2-blue" />
                            <span className="text-sm text-c2-white">Intégrer un équipement</span>
                          </button>

                          <div className="text-xs text-c2-gray-100 px-2">
                            <p>• Types d'équipements disponibles</p>
                            <p>• Intégration et déploiement</p>
                            <p>• Gestion des statuts</p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* ÉQUIPEMENTS EXISTANTS */}
                {Object.entries(EQUIPMENT_TYPES).map(([key, type]) => {
                  const categoryKey = key.toLowerCase();
                  const isExpanded = expandedCategories[categoryKey];

                  // Combiner équipements mockés et intégrés
                  const mockedEquipment = equipmentByType[categoryKey] || [];
                  const integratedForType = integratedEquipment.filter(eq =>
                    eq.equipment_type?.toLowerCase() === categoryKey
                  );
                  const equipment = [...mockedEquipment, ...integratedForType];

                  // Debug
                  if (categoryKey === 'comint') {
                    console.log('🔧 COMINT - Mockés:', mockedEquipment.length, 'Intégrés:', integratedForType.length, 'Total:', equipment.length);
                    console.log('🔧 Équipements intégrés disponibles:', integratedEquipment);
                  }

                  const Icon = getEquipmentIcon(categoryKey);

                  return (
                    <div key={categoryKey} className="border-b border-c2-gray-300">
                      <button
                        onClick={() => toggleCategory(categoryKey)}
                        className="w-full p-3 flex items-center justify-between hover:bg-c2-gray-300 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Icon className="w-5 h-5 text-c2-blue" />
                          <span className="text-sm font-medium text-c2-white">{type.name}</span>
                          <span className="text-xs text-c2-gray-100 bg-c2-gray-300 px-2 py-1 rounded">
                            {equipment.length}
                          </span>
                        </div>
                        {isExpanded ? (
                          <ChevronDown className="w-4 h-4 text-c2-gray-100" />
                        ) : (
                          <ChevronRight className="w-4 h-4 text-c2-gray-100" />
                        )}
                      </button>

                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="overflow-hidden"
                          >
                            <div className="px-4 pb-2">
                              {equipment.length > 0 ? (
                                equipment.map((item) => (
                                  <button
                                    key={item.id}
                                    onClick={() => handleEquipmentClick(item)}
                                    className="w-full p-2 flex items-center space-x-3 hover:bg-c2-gray-300 rounded transition-colors text-left"
                                  >
                                    {getStatusIndicator(item.status)}
                                    <div className="flex-1 min-w-0">
                                      <div className="text-sm text-c2-white truncate">{item.name}</div>
                                      <div className="text-xs text-c2-gray-100">
                                        {typeof item.location === 'object'
                                          ? `${item.location?.latitude?.toFixed(4)}, ${item.location?.longitude?.toFixed(4)}`
                                          : item.location || 'Position non définie'
                                        }
                                      </div>
                                    </div>
                                  </button>
                                ))
                              ) : (
                                <div className="p-2 text-xs text-c2-gray-100 text-center">
                                  Aucun équipement
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  );
                })}

                {/* SÉPARATEUR */}
                <div className="p-4 border-b border-c2-gray-300">
                  <h2 className="text-lg font-semibold text-c2-white">Outils Tactiques C2</h2>
                </div>

                {/* OUTILS TACTIQUES */}
                <div className="border-b border-c2-gray-300">
                  <button
                    onClick={() => toggleCategory('tactical')}
                    className="w-full p-3 flex items-center justify-between hover:bg-c2-gray-300 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Target className="w-5 h-5 text-c2-blue" />
                      <span className="text-sm font-medium text-c2-white">Outils Tactiques</span>
                    </div>
                    {expandedCategories.tactical ? (
                      <ChevronDown className="w-4 h-4 text-c2-gray-100" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-c2-gray-100" />
                    )}
                  </button>

                  <AnimatePresence>
                    {expandedCategories.tactical && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="overflow-hidden"
                      >
                        <div className="p-2 space-y-2">
                          {tacticalTools.map((tool) => {
                            const Icon = tool.icon;
                            const isActive = activeMapTool === tool.id;
                            return (
                              <button
                                key={tool.id}
                                onClick={() => handleTacticalTool(tool.id)}
                                className={`w-full p-3 flex items-center space-x-3 rounded-lg transition-all duration-200 border-2 ${isActive
                                  ? 'bg-c2-blue text-white shadow-lg border-green-400 transform scale-105'
                                  : 'hover:bg-c2-gray-300 text-c2-gray-100 border-transparent hover:border-c2-gray-200'
                                  }`}
                              >
                                <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-c2-blue'}`} />
                                <span className="text-sm font-medium flex-1 text-left">{tool.name}</span>
                                <div className="flex items-center space-x-2">
                                  {isActive ? (
                                    <>
                                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                      <span className="text-xs font-bold">ACTIF</span>
                                    </>
                                  ) : (
                                    <span className="text-xs text-c2-gray-200">INACTIF</span>
                                  )}
                                </div>
                              </button>
                            );
                          })}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Modal de gestion d'équipements */}
      <AnimatePresence>
        {showEquipmentManager && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowEquipmentManager(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-c2-gray-400 rounded-lg w-[90vw] h-[80vh] max-w-6xl"
              onClick={(e) => e.stopPropagation()}
            >
              <EquipmentManager
                onEquipmentSelect={(equipment) => {
                  setShowEquipmentManager(false);
                  if (onEquipmentSelect) {
                    onEquipmentSelect(equipment);
                  }
                }}
                onClose={() => setShowEquipmentManager(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default SidebarLeftModern;
