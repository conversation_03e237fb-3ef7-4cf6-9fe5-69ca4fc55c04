import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Settings,
  Activity,
  Wifi,
  WifiOff,
  AlertTriangle,
  Play,
  Pause,
  Square,
  RotateCw,
  ChevronRight,
  ChevronDown,
  Radio,
  Radar,
  Shield,
  Zap
} from 'lucide-react';
import { useEquipmentStore } from '@/store/equipmentStore';
import { useCommandStore } from '@/store/commandStore';
import { LAYOUT, STATUS_COLORS, EQUIPMENT_TYPES } from '@/utils/constants';

const SidebarRight = ({ isOpen, onToggle, equipmentId }) => {
  const [activeTab, setActiveTab] = useState('details');
  const [expandedSections, setExpandedSections] = useState({
    status: true,
    metrics: true,
    commands: false,
    history: false,
  });

  const { getEquipmentById, getEquipmentMetrics } = useEquipmentStore();
  const { sendCommand, commandHistory } = useCommandStore();

  const equipment = equipmentId ? getEquipmentById(equipmentId) : null;
  const metrics = equipmentId ? getEquipmentMetrics(equipmentId) : null;

  useEffect(() => {
    if (equipmentId) {
      // Charger les métriques de l'équipement une seule fois
      getEquipmentMetrics(equipmentId);

      // Puis les recharger toutes les 30 secondes seulement
      const interval = setInterval(() => {
        getEquipmentMetrics(equipmentId);
      }, 30000); // 30 secondes au lieu de boucle infinie

      return () => clearInterval(interval);
    }
  }, [equipmentId]); // Suppression de getEquipmentMetrics des dépendances

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleCommand = async (command, parameters = {}) => {
    if (!equipmentId) return;

    try {
      await sendCommand(equipmentId, command, parameters);
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la commande:', error);
    }
  };

  const getEquipmentIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'comint':
        return Radio;
      case 'elint':
        return Radar;
      case 'anti_drone':
        return Shield;
      case 'jammer':
        return Zap;
      default:
        return Radio;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <Wifi className="w-4 h-4 text-status-active" />;
      case 'standby':
        return <Settings className="w-4 h-4 text-status-standby" />;
      case 'offline':
        return <WifiOff className="w-4 h-4 text-status-offline" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-status-offline" />;
      default:
        return <WifiOff className="w-4 h-4 text-status-unknown" />;
    }
  };

  const formatMetricValue = (value, unit = '') => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'number') {
      return `${value.toFixed(2)}${unit}`;
    }
    return value;
  };

  const tabs = [
    { id: 'details', name: 'Détails', icon: Settings },
    { id: 'metrics', name: 'Métriques', icon: Activity },
    { id: 'commands', name: 'Commandes', icon: Play },
  ];

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ x: LAYOUT.SIDEBAR_WIDTH }}
      animate={{ x: 0 }}
      exit={{ x: LAYOUT.SIDEBAR_WIDTH }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="fixed right-0 top-16 h-full bg-c2-gray-400 border-l border-c2-gray-300 z-40 shadow-lg"
      style={{ width: LAYOUT.SIDEBAR_WIDTH }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-c2-gray-300">
        <h2 className="text-lg font-semibold text-c2-white">
          {equipment ? 'Détails Équipement' : 'Aucun équipement sélectionné'}
        </h2>
        <button
          onClick={() => onToggle(false)}
          className="p-1 hover:bg-c2-gray-300 rounded transition-colors"
        >
          <X className="w-5 h-5 text-c2-gray-100" />
        </button>
      </div>

      {equipment ? (
        <>
          {/* Informations de base */}
          <div className="p-4 border-b border-c2-gray-300">
            <div className="flex items-center space-x-3 mb-3">
              {React.createElement(getEquipmentIcon(equipment.equipment_type), {
                className: "w-6 h-6",
                style: { color: EQUIPMENT_TYPES[equipment.equipment_type?.toUpperCase()]?.color || '#2196F3' }
              })}
              <div className="flex-1">
                <h3 className="font-semibold text-c2-white">{equipment.name}</h3>
                <p className="text-sm text-c2-gray-100">{equipment.description}</p>
              </div>
              <div className="flex items-center space-x-1">
                {getStatusIcon(equipment.status)}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-c2-gray-100">Type:</span>
                <div className="text-c2-white font-medium">
                  {EQUIPMENT_TYPES[equipment.equipment_type?.toUpperCase()]?.name || equipment.equipment_type}
                </div>
              </div>
              <div>
                <span className="text-c2-gray-100">Statut:</span>
                <div className={`font-medium ${equipment.status === 'active' ? 'text-status-active' :
                  equipment.status === 'standby' ? 'text-status-standby' :
                    equipment.status === 'offline' ? 'text-status-offline' :
                      'text-status-unknown'
                  }`}>
                  {equipment.status?.toUpperCase()}
                </div>
              </div>
            </div>
          </div>

          {/* Onglets */}
          <div className="flex border-b border-c2-gray-300">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-3 px-2 transition-colors ${activeTab === tab.id
                    ? 'bg-c2-blue text-white border-b-2 border-c2-blue'
                    : 'text-c2-gray-100 hover:bg-c2-gray-300'
                    }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-sm">{tab.name}</span>
                </button>
              );
            })}
          </div>

          {/* Contenu des onglets */}
          <div className="flex-1 overflow-y-auto">
            <AnimatePresence mode="wait">
              {activeTab === 'details' && (
                <motion.div
                  key="details"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="p-4 space-y-4"
                >
                  {/* Section Statut */}
                  <div>
                    <button
                      onClick={() => toggleSection('status')}
                      className="w-full flex items-center justify-between py-2 text-c2-white font-medium"
                    >
                      <span>Informations Système</span>
                      {expandedSections.status ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </button>

                    {expandedSections.status && (
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-c2-gray-100">Modèle:</span>
                          <span className="text-c2-white">{equipment.model || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-c2-gray-100">Série:</span>
                          <span className="text-c2-white font-mono">{equipment.serial_number || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-c2-gray-100">IP:</span>
                          <span className="text-c2-white font-mono">{equipment.ip_address || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-c2-gray-100">Port:</span>
                          <span className="text-c2-white">{equipment.port || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-c2-gray-100">Dernière activité:</span>
                          <span className="text-c2-white">
                            {equipment.last_seen ? new Date(equipment.last_seen).toLocaleString('fr-FR') : 'Jamais'}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Section Configuration */}
                  <div>
                    <button
                      onClick={() => toggleSection('config')}
                      className="w-full flex items-center justify-between py-2 text-c2-white font-medium"
                    >
                      <span>Configuration</span>
                      {expandedSections.config ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </button>

                    {expandedSections.config && (
                      <div className="space-y-2 text-sm">
                        {equipment.configuration && Object.entries(equipment.configuration).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-c2-gray-100 capitalize">{key.replace('_', ' ')}:</span>
                            <span className="text-c2-white">{String(value)}</span>
                          </div>
                        ))}
                        {(!equipment.configuration || Object.keys(equipment.configuration).length === 0) && (
                          <span className="text-c2-gray-100">Aucune configuration disponible</span>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}

              {activeTab === 'metrics' && (
                <motion.div
                  key="metrics"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="p-4 space-y-4"
                >
                  {metrics ? (
                    <>
                      {/* Métriques système */}
                      <div>
                        <h4 className="font-medium text-c2-white mb-2">Système</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-c2-gray-100">CPU:</span>
                            <span className="text-c2-white">{formatMetricValue(metrics.cpu_usage, '%')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-c2-gray-100">Mémoire:</span>
                            <span className="text-c2-white">{formatMetricValue(metrics.memory_usage, '%')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-c2-gray-100">Température:</span>
                            <span className="text-c2-white">{formatMetricValue(metrics.temperature, '°C')}</span>
                          </div>
                        </div>
                      </div>

                      {/* Métriques RF */}
                      <div>
                        <h4 className="font-medium text-c2-white mb-2">Radio Fréquence</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-c2-gray-100">Signal:</span>
                            <span className="text-c2-white">{formatMetricValue(metrics.signal_strength, ' dBm')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-c2-gray-100">Bruit:</span>
                            <span className="text-c2-white">{formatMetricValue(metrics.noise_floor, ' dBm')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-c2-gray-100">SNR:</span>
                            <span className="text-c2-white">{formatMetricValue(metrics.snr, ' dB')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-c2-gray-100">Fréquence:</span>
                            <span className="text-c2-white">{formatMetricValue(metrics.frequency / 1000000, ' MHz')}</span>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center text-c2-gray-100 py-8">
                      <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>Aucune métrique disponible</p>
                    </div>
                  )}
                </motion.div>
              )}

              {activeTab === 'commands' && (
                <motion.div
                  key="commands"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="p-4 space-y-4"
                >
                  {/* Commandes rapides */}
                  <div>
                    <h4 className="font-medium text-c2-white mb-3">Commandes Rapides</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => handleCommand('start')}
                        className="c2-button flex items-center justify-center space-x-2 py-2"
                      >
                        <Play className="w-4 h-4" />
                        <span>Démarrer</span>
                      </button>
                      <button
                        onClick={() => handleCommand('stop')}
                        className="c2-button flex items-center justify-center space-x-2 py-2"
                      >
                        <Square className="w-4 h-4" />
                        <span>Arrêter</span>
                      </button>
                      <button
                        onClick={() => handleCommand('restart')}
                        className="c2-button flex items-center justify-center space-x-2 py-2"
                      >
                        <RotateCw className="w-4 h-4" />
                        <span>Redémarrer</span>
                      </button>
                      <button
                        onClick={() => handleCommand('status')}
                        className="c2-button flex items-center justify-center space-x-2 py-2"
                      >
                        <Activity className="w-4 h-4" />
                        <span>Statut</span>
                      </button>
                    </div>
                  </div>

                  {/* Historique des commandes */}
                  <div>
                    <h4 className="font-medium text-c2-white mb-3">Historique</h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {commandHistory.filter(cmd => cmd.equipment_id === equipmentId).slice(0, 10).map((command) => (
                        <div key={command.id} className="text-sm p-2 bg-c2-gray-300 rounded">
                          <div className="flex justify-between items-center">
                            <span className="text-c2-white font-medium">{command.name}</span>
                            <span className={`text-xs px-2 py-1 rounded ${command.status === 'completed' ? 'bg-status-active text-white' :
                              command.status === 'failed' ? 'bg-status-offline text-white' :
                                'bg-status-standby text-white'
                              }`}>
                              {command.status}
                            </span>
                          </div>
                          <div className="text-c2-gray-100 text-xs mt-1">
                            {new Date(command.created_at).toLocaleString('fr-FR')}
                          </div>
                        </div>
                      ))}
                      {commandHistory.filter(cmd => cmd.equipment_id === equipmentId).length === 0 && (
                        <div className="text-center text-c2-gray-100 py-4">
                          <p>Aucune commande dans l'historique</p>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-c2-gray-100">
            <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Sélectionnez un équipement pour voir ses détails</p>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default SidebarRight;
