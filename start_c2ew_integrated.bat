@echo off
title C2-EW Platform - Lancement Intégré avec Module iCOM
color 0A

echo.
echo ========================================
echo    C2-EW Platform - INTÉGRATION COMPLÈTE
echo    Plateforme + Module iCOM Intégré
echo ========================================
echo    Version: 1.0.0 - Intégration iCOM
echo ========================================
echo.

echo ARCHITECTURE INTÉGRÉE:
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                    C2-EW Platform                           │
echo │  Frontend (3000) + Backend (8000) + Tiles (3001)          │
echo │                         │                                   │
echo │                    ┌────▼────┐                             │
echo │                    │ Proxy   │                             │
echo │                    │ iCOM    │                             │
echo │                    └────┬────┘                             │
echo │                         │                                   │
echo │  ┌──────────────────────▼──────────────────────────────┐   │
echo │  │              Module iCOM                            │   │
echo │  │  Backend (8100) + Audio (8102) + Hardware          │   │
echo │  └─────────────────────────────────────────────────────┘   │
echo └─────────────────────────────────────────────────────────────┘
echo.

echo SERVICES INTÉGRÉS:
echo ✅ Plateforme C2 EW (ports 3000, 8000, 3001)
echo ✅ Module iCOM Backend (port 8100)
echo ✅ Module iCOM Audio (port 8102)
echo ✅ Interface iCOM intégrée dans la carte
echo ✅ Gestion d'équipements unifiée
echo.

echo FONCTIONNALITÉS:
echo 🎯 Intégration d'équipements via menu latéral
echo 🗺️  Déploiement sur carte avec coordonnées
echo 📡 Interface iCOM complète dans la zone centrale
echo 🎵 Audio streaming intégré
echo 📊 Gestion des statuts en temps réel
echo.

set /p ready="Appuyez sur ENTRÉE pour démarrer l'intégration complète..."

echo.
echo [1/6] Vérification des prérequis...

REM Vérifier la plateforme C2 EW
if not exist "platfrom\backend\main.py" (
    echo ❌ Backend C2 EW manquant
    pause
    exit /b 1
)
echo ✅ Backend C2 EW trouvé

if not exist "platfrom\frontend\package.json" (
    echo ❌ Frontend C2 EW manquant
    pause
    exit /b 1
)
echo ✅ Frontend C2 EW trouvé

REM Vérifier le module iCOM
if not exist "module iCOM\backend\main.py" (
    echo ❌ Backend iCOM manquant
    pause
    exit /b 1
)
echo ✅ Backend iCOM trouvé

if not exist "module iCOM\.venv\Scripts\python.exe" (
    echo ❌ Environnement virtuel iCOM manquant
    pause
    exit /b 1
)
echo ✅ Environnement virtuel iCOM trouvé

echo.
echo [2/6] Démarrage du serveur de tuiles C2 EW (port 3001)...
cd platfrom
start "C2-EW Tiles Server" cmd /k "python -m http.server 3001"
echo ✅ Serveur de tuiles lancé
timeout /t 2 /nobreak > nul

echo.
echo [3/6] Démarrage du serveur audio iCOM (port 8102)...
cd "..\module iCOM\backend"
start "iCOM Audio Server" cmd /k "..\.venv\Scripts\python.exe audio_live_server_ameliore.py"
echo ✅ Serveur audio iCOM lancé
timeout /t 3 /nobreak > nul

echo.
echo [4/6] Démarrage du backend iCOM (port 8100)...
start "iCOM Backend" cmd /k "..\.venv\Scripts\python.exe main.py"
echo ✅ Backend iCOM lancé
timeout /t 4 /nobreak > nul

echo.
echo [5/6] Démarrage du backend C2 EW (port 8000)...
cd "..\..\platfrom\backend"
start "C2-EW Backend" cmd /k "uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
echo ✅ Backend C2 EW lancé
timeout /t 4 /nobreak > nul

echo.
echo [6/7] Démarrage du frontend iCOM (port 5173)...
cd "..\..\module iCOM\frontend"
start "iCOM Frontend" cmd /k "npm run dev:noopen"
echo ✅ Frontend iCOM lancé
timeout /t 3 /nobreak > nul

echo.
echo [7/7] Démarrage du frontend C2 EW intégré (port 3000)...
cd "..\..\platfrom\frontend"
start "C2-EW Frontend Intégré" cmd /k "npm run dev"
echo ✅ Frontend C2 EW intégré lancé
timeout /t 3 /nobreak > nul

echo.
echo ========================================
echo    INTÉGRATION COMPLÈTE ACTIVE !
echo ========================================
echo.

echo 🌐 SERVICES ACTIFS:
echo   🎨 Frontend C2 EW:      http://localhost:3000
echo   🔧 Backend C2 EW:       http://localhost:8000
echo   🗺️  Serveur de tuiles:   http://localhost:3001
echo   📡 Backend iCOM:        http://localhost:8100
echo   🎵 Serveur Audio iCOM:  ws://localhost:8102
echo   🖥️  Frontend iCOM:       http://localhost:5173
echo.

echo 📚 DOCUMENTATION:
echo   📖 API C2 EW:          http://localhost:8000/docs
echo   📖 API iCOM:           http://localhost:8100/docs
echo.

echo ========================================
echo    GUIDE D'UTILISATION INTÉGRÉE
echo ========================================
echo.

echo 1️⃣ INTÉGRER UN ÉQUIPEMENT iCOM:
echo   • Ouvrir le menu latéral gauche
echo   • Cliquer sur "Gestion d'Équipements"
echo   • Sélectionner "Intégrer un équipement"
echo   • Choisir COMINT → ICOM IC-R8600
echo   • Donner un nom à votre station
echo.

echo 2️⃣ DÉPLOYER SUR LA CARTE:
echo   • Dans la liste des équipements intégrés
echo   • Cliquer sur "Déployer" pour votre iCOM
echo   • Saisir les coordonnées GPS
echo   • Valider le déploiement
echo.

echo 3️⃣ UTILISER L'INTERFACE iCOM:
echo   • Cliquer sur l'icône iCOM sur la carte
echo   • L'interface s'ouvre dans la zone centrale
echo   • Toutes les fonctions originales disponibles
echo   • Audio streaming automatique
echo.

echo ========================================
echo    DIAGNOSTIC ET DÉPANNAGE
echo ========================================
echo.

echo Test des services...
timeout /t 5 /nobreak > nul

echo Vérification des connexions:
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 3000); $tcp.Close(); Write-Host '✅ Frontend C2 EW accessible' } catch { Write-Host '❌ Frontend C2 EW non accessible' }"
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 8000); $tcp.Close(); Write-Host '✅ Backend C2 EW accessible' } catch { Write-Host '❌ Backend C2 EW non accessible' }"
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 8100); $tcp.Close(); Write-Host '✅ Backend iCOM accessible' } catch { Write-Host '❌ Backend iCOM non accessible' }"
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 8102); $tcp.Close(); Write-Host '✅ Serveur audio iCOM accessible' } catch { Write-Host '❌ Serveur audio iCOM non accessible' }"
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 5173); $tcp.Close(); Write-Host '✅ Frontend iCOM accessible' } catch { Write-Host '❌ Frontend iCOM non accessible' }"

echo.
echo ========================================
echo    INTÉGRATION PRÊTE !
echo ========================================
echo.

echo 🚀 L'application C2 EW avec module iCOM intégré est maintenant active !
echo.

echo 🔗 Accès principal: http://localhost:3000
echo.

echo 📋 FONCTIONNALITÉS DISPONIBLES:
echo   ✅ Carte interactive du Maroc
echo   ✅ Gestion d'équipements unifiée
echo   ✅ Interface iCOM intégrée
echo   ✅ Audio streaming en temps réel
echo   ✅ Déploiement par coordonnées
echo   ✅ Statuts en temps réel
echo.

echo 🔧 CONTRÔLES DISPONIBLES:
echo   • Molette de fréquence iCOM
echo   • Boutons de mode et filtres
echo   • Contrôles audio et gain
echo   • Enregistrement et streaming
echo   • Fréquences tactiques prédéfinies
echo.

echo ⚠️  IMPORTANT:
echo   • Gardez toutes les fenêtres de services ouvertes
echo   • Le récepteur iCOM doit être connecté via USB
echo   • L'interface s'ouvre dans la carte (pas de nouvelle fenêtre)
echo.

echo Pour arrêter l'intégration:
echo 1. Fermer toutes les fenêtres de services
echo 2. Ou fermer cette fenêtre principale
echo.

echo ========================================
echo    INTÉGRATION C2 EW + iCOM ACTIVE !
echo ========================================
echo.

echo Ouverture automatique du navigateur...
timeout /t 3 /nobreak > nul
start http://localhost:3000

echo.
echo L'intégration est maintenant complète et opérationnelle !
echo Consultez le guide ci-dessus pour utiliser les fonctionnalités.
echo.

pause
